# Database Configuration
DB_NAME=language_center_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432

# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=English Language Center <<EMAIL>>

# Email Backend Options:
# For production: EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
# For testing: EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
# For file-based testing: EMAIL_BACKEND=django.core.mail.backends.filebased.EmailBackend
