<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Language Center - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1d4ed8',
                        accent: '#10b981',
                        dark: '#1e293b',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        .input-focus:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }
        .login-card {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .language-selector:hover .language-dropdown {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <!-- Language Selector -->
    <div class="absolute top-4 right-4 z-10 language-selector">
        <button class="flex items-center text-gray-600 hover:text-gray-800">
            <i class="fas fa-globe mr-2"></i>
            <span>English</span>
            <i class="fas fa-chevron-down ml-1 text-xs"></i>
        </button>
        <div class="language-dropdown hidden absolute right-0 mt-2 w-40 bg-white rounded-md shadow-lg z-20">
            <div class="py-1">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">English</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Español</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">中文</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">العربية</a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex flex-col md:flex-row w-full max-w-6xl rounded-xl overflow-hidden login-card">
        <!-- Left Side - Branding -->
        <div class="gradient-bg text-white p-8 md:p-12 md:w-1/2 flex flex-col justify-center items-center text-center">
            <div class="fade-in mb-8">
                <div class="flex items-center justify-center mb-6">
                    <i class="fas fa-book-open text-4xl mr-3"></i>
                    <h1 class="text-3xl font-bold">English Language Center</h1>
                </div>
                <p class="text-lg opacity-90 mb-8">Master English with our expert teachers and comprehensive curriculum</p>
            </div>
            
            <div class="fade-in w-full max-w-md">
                <div class="flex items-center mb-6">
                    <div class="flex-1 border-t border-blue-300"></div>
                    <span class="px-4 text-blue-200">Why choose us?</span>
                    <div class="flex-1 border-t border-blue-300"></div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Certified Teachers</h4>
                            <p class="text-sm opacity-80">Native speakers with TEFL certification</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Flexible Schedule</h4>
                            <p class="text-sm opacity-80">Morning, afternoon and evening classes</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Modern Methods</h4>
                            <p class="text-sm opacity-80">Interactive and communicative approach</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Progress Tracking</h4>
                            <p class="text-sm opacity-80">Regular assessments and feedback</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Login Form -->
        <div class="bg-white p-8 md:p-12 md:w-1/2 flex flex-col justify-center fade-in" style="animation-delay: 0.2s;">
            <div class="max-w-md mx-auto w-full">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Welcome back</h2>
                <p class="text-gray-600 mb-8">Please enter your credentials to access your account</p>
                
                <form class="space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input id="email" name="email" type="email" autocomplete="email" required 
                                   class="input-focus pl-10 block w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div>
                        <div class="flex justify-between items-center mb-1">
                            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                            <a href="#" class="text-sm text-primary hover:text-secondary">Forgot password?</a>
                        </div>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input id="password" name="password" type="password" autocomplete="current-password" required 
                                   class="input-focus pl-10 block w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" 
                                   placeholder="••••••••">
                            <button type="button" class="absolute right-3 top-3 text-gray-400 hover:text-gray-600" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox" 
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="remember-me" class="ml-2 block text-sm text-gray-700">Remember me</label>
                    </div>
                    
                    <div>
                        <button type="submit" 
                                class="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                            Sign in
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </form>
                
                <div class="mt-6">
                    <div class="relative">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gray-300"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-2 bg-white text-gray-500">Or continue with</span>
                        </div>
                    </div>
                    
                    <div class="mt-6 grid grid-cols-2 gap-3">
                        <a href="#" class="w-full inline-flex justify-center items-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <i class="fab fa-google text-red-500 mr-2"></i>
                            Google
                        </a>
                        <a href="#" class="w-full inline-flex justify-center items-center py-2 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <i class="fab fa-microsoft text-blue-500 mr-2"></i>
                            Microsoft
                        </a>
                    </div>
                </div>
                
                <div class="mt-8 text-center text-sm text-gray-600">
                    <p>Don't have an account? <a href="#" class="text-primary font-medium hover:text-secondary">Contact administration</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="absolute bottom-4 left-0 right-0 text-center text-xs text-gray-500">
        <p>© 2023 English Language Center. All rights reserved. <a href="#" class="text-primary hover:text-secondary">Privacy Policy</a> | <a href="#" class="text-primary hover:text-secondary">Terms of Service</a></p>
    </div>

    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.replace('fa-eye', 'fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.replace('fa-eye-slash', 'fa-eye');
            }
        });

        // Simple form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                alert('Please fill in all fields');
                return;
            }
            
            // In a real application, you would send this to your server
            console.log('Login attempt with:', { email, password });
            
            // Simulate successful login
            // window.location.href = 'dashboard.html';
        });

        // Language selector toggle
        document.querySelector('.language-selector').addEventListener('click', function(e) {
            e.stopPropagation();
            document.querySelector('.language-dropdown').classList.toggle('hidden');
        });

        // Close language dropdown when clicking outside
        document.addEventListener('click', function() {
            document.querySelector('.language-dropdown').classList.add('hidden');
        });
    </script>
</body>
</html>