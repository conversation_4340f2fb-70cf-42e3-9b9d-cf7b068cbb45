"""
Email service functions for sending payment reminders and notifications
"""
import logging
from datetime import date, timedelta
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.utils import timezone
from .models import Student, Payment, EmailNotification, PaymentReminderSettings

logger = logging.getLogger(__name__)


class EmailService:
    """Service class for handling email notifications"""
    
    def __init__(self):
        self.settings = PaymentReminderSettings.get_settings()
    
    def send_payment_reminder(self, student, payment, days_until_due):
        """Send payment reminder email to student"""
        try:
            # Check if notification already sent for this payment and reminder type
            notification_exists = EmailNotification.objects.filter(
                student=student,
                payment=payment,
                notification_type='PAYMENT_REMINDER',
                scheduled_date__date=date.today()
            ).exists()
            
            if notification_exists:
                logger.info(f"Payment reminder already sent to {student.email} for payment {payment.id}")
                return False
            
            # Prepare email context
            context = {
                'student': student,
                'payment': payment,
                'days_until_due': days_until_due,
                'language_center_name': 'English Language Center',
            }
            
            # Render email content
            html_message = render_to_string('center/emails/payment_reminder.html', context)
            plain_message = strip_tags(html_message)
            
            subject = f"Payment Reminder - Due in {days_until_due} day{'s' if days_until_due != 1 else ''}"
            
            # Create notification record
            notification = EmailNotification.objects.create(
                student=student,
                payment=payment,
                notification_type='PAYMENT_REMINDER',
                subject=subject,
                message=plain_message,
                scheduled_date=timezone.now(),
                status='PENDING'
            )
            
            # Send email
            success = self._send_email(
                subject=subject,
                message=plain_message,
                html_message=html_message,
                recipient_list=[student.email],
                notification=notification
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending payment reminder to {student.email}: {str(e)}")
            return False
    
    def send_overdue_notification(self, student, payment, days_overdue):
        """Send overdue payment notification to student"""
        try:
            # Check if notification already sent for this payment and overdue period
            notification_exists = EmailNotification.objects.filter(
                student=student,
                payment=payment,
                notification_type='PAYMENT_OVERDUE',
                scheduled_date__date=date.today()
            ).exists()
            
            if notification_exists:
                logger.info(f"Overdue notification already sent to {student.email} for payment {payment.id}")
                return False
            
            # Prepare email context
            context = {
                'student': student,
                'payment': payment,
                'days_overdue': days_overdue,
                'language_center_name': 'English Language Center',
            }
            
            # Render email content
            html_message = render_to_string('center/emails/payment_overdue.html', context)
            plain_message = strip_tags(html_message)
            
            subject = f"URGENT: Payment Overdue - {days_overdue} day{'s' if days_overdue != 1 else ''}"
            
            # Create notification record
            notification = EmailNotification.objects.create(
                student=student,
                payment=payment,
                notification_type='PAYMENT_OVERDUE',
                subject=subject,
                message=plain_message,
                scheduled_date=timezone.now(),
                status='PENDING'
            )
            
            # Send email
            success = self._send_email(
                subject=subject,
                message=plain_message,
                html_message=html_message,
                recipient_list=[student.email],
                notification=notification
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending overdue notification to {student.email}: {str(e)}")
            return False
    
    def send_payment_confirmation(self, student, payment):
        """Send payment confirmation email to student"""
        try:
            # Prepare email context
            context = {
                'student': student,
                'payment': payment,
                'language_center_name': 'English Language Center',
            }
            
            # Render email content
            html_message = render_to_string('center/emails/payment_confirmation.html', context)
            plain_message = strip_tags(html_message)
            
            subject = f"Payment Confirmation - ${payment.amount} Received"
            
            # Create notification record
            notification = EmailNotification.objects.create(
                student=student,
                payment=payment,
                notification_type='PAYMENT_CONFIRMATION',
                subject=subject,
                message=plain_message,
                scheduled_date=timezone.now(),
                status='PENDING'
            )
            
            # Send email
            success = self._send_email(
                subject=subject,
                message=plain_message,
                html_message=html_message,
                recipient_list=[student.email],
                notification=notification
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending payment confirmation to {student.email}: {str(e)}")
            return False
    
    def _send_email(self, subject, message, html_message, recipient_list, notification=None):
        """Internal method to send email and update notification status"""
        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=self.settings.from_email,
                recipient_list=recipient_list,
                html_message=html_message,
                fail_silently=False
            )
            
            if notification:
                notification.status = 'SENT'
                notification.sent_date = timezone.now()
                notification.save()
            
            logger.info(f"Email sent successfully to {', '.join(recipient_list)}")
            return True
            
        except Exception as e:
            error_message = str(e)
            logger.error(f"Failed to send email to {', '.join(recipient_list)}: {error_message}")
            
            if notification:
                notification.status = 'FAILED'
                notification.error_message = error_message
                notification.save()
            
            return False
    
    def get_students_needing_reminders(self):
        """Get students who need payment reminders"""
        reminder_days = self.settings.reminder_days_list
        students_to_remind = []
        
        for days in reminder_days:
            target_date = date.today() + timedelta(days=days)
            
            # Find payments due on target date
            payments = Payment.objects.filter(
                next_due_date=target_date,
                is_confirmed=True,
                student__is_active=True
            ).select_related('student')
            
            for payment in payments:
                # Check if reminder already sent
                reminder_sent = EmailNotification.objects.filter(
                    student=payment.student,
                    payment=payment,
                    notification_type='PAYMENT_REMINDER',
                    scheduled_date__date=date.today(),
                    status='SENT'
                ).exists()
                
                if not reminder_sent:
                    students_to_remind.append({
                        'student': payment.student,
                        'payment': payment,
                        'days_until_due': days
                    })
        
        return students_to_remind
    
    def get_students_with_overdue_payments(self):
        """Get students with overdue payments"""
        overdue_days = self.settings.overdue_days_list
        students_overdue = []
        
        for days in overdue_days:
            target_date = date.today() - timedelta(days=days)
            
            # Find overdue payments
            payments = Payment.objects.filter(
                next_due_date=target_date,
                is_confirmed=True,
                student__is_active=True
            ).select_related('student')
            
            for payment in payments:
                # Check if overdue notification already sent
                notification_sent = EmailNotification.objects.filter(
                    student=payment.student,
                    payment=payment,
                    notification_type='PAYMENT_OVERDUE',
                    scheduled_date__date=date.today(),
                    status='SENT'
                ).exists()
                
                if not notification_sent:
                    students_overdue.append({
                        'student': payment.student,
                        'payment': payment,
                        'days_overdue': days
                    })
        
        return students_overdue


def send_daily_payment_reminders():
    """Function to send daily payment reminders - can be called by management command"""
    email_service = EmailService()
    
    if not email_service.settings.email_enabled:
        logger.info("Email notifications are disabled")
        return
    
    # Send payment reminders
    students_to_remind = email_service.get_students_needing_reminders()
    reminder_count = 0
    
    for item in students_to_remind:
        success = email_service.send_payment_reminder(
            item['student'], 
            item['payment'], 
            item['days_until_due']
        )
        if success:
            reminder_count += 1
    
    # Send overdue notifications
    students_overdue = email_service.get_students_with_overdue_payments()
    overdue_count = 0
    
    for item in students_overdue:
        success = email_service.send_overdue_notification(
            item['student'], 
            item['payment'], 
            item['days_overdue']
        )
        if success:
            overdue_count += 1
    
    logger.info(f"Daily email summary: {reminder_count} reminders sent, {overdue_count} overdue notifications sent")
    return reminder_count, overdue_count
