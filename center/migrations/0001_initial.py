# Generated by Django 4.2.7 on 2025-07-12 21:23

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Student',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=100, verbose_name='Full Name')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email Address')),
                ('phone_number', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')], verbose_name='Phone Number')),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other')], max_length=1, verbose_name='Gender')),
                ('date_of_birth', models.DateField(verbose_name='Date of Birth')),
                ('registration_date', models.DateTimeField(auto_now_add=True, verbose_name='Registration Date')),
                ('level', models.CharField(choices=[('BEGINNER', 'Beginner'), ('ELEMENTARY', 'Elementary'), ('INTERMEDIATE', 'Intermediate'), ('UPPER_INTERMEDIATE', 'Upper-Intermediate'), ('ADVANCED', 'Advanced'), ('PROFICIENCY', 'Proficiency')], max_length=20, verbose_name='Current Level')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active Status')),
            ],
            options={
                'verbose_name': 'Student',
                'verbose_name_plural': 'Students',
                'ordering': ['full_name'],
            },
        ),
        migrations.CreateModel(
            name='Teacher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Full Name')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email Address')),
                ('phone_number', models.CharField(max_length=17, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')], verbose_name='Phone Number')),
                ('subject_specialization', models.CharField(max_length=100, verbose_name='Subject Specialization')),
                ('date_joined', models.DateTimeField(auto_now_add=True, verbose_name='Date Joined')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active Status')),
            ],
            options={
                'verbose_name': 'Teacher',
                'verbose_name_plural': 'Teachers',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Payment Date')),
                ('payment_method', models.CharField(choices=[('CASH', 'Cash'), ('CARD', 'Credit/Debit Card'), ('BANK_TRANSFER', 'Bank Transfer'), ('ONLINE', 'Online Payment'), ('CHECK', 'Check')], max_length=15, verbose_name='Payment Method')),
                ('next_due_date', models.DateField(verbose_name='Next Due Date')),
                ('is_confirmed', models.BooleanField(default=True, verbose_name='Payment Confirmed')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Record Created')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='center.student', verbose_name='Student')),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-payment_date'],
            },
        ),
        migrations.CreateModel(
            name='Class',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('class_name', models.CharField(max_length=100, verbose_name='Class Name')),
                ('level', models.CharField(choices=[('BEGINNER', 'Beginner'), ('ELEMENTARY', 'Elementary'), ('INTERMEDIATE', 'Intermediate'), ('UPPER_INTERMEDIATE', 'Upper-Intermediate'), ('ADVANCED', 'Advanced'), ('PROFICIENCY', 'Proficiency')], max_length=20, verbose_name='Level')),
                ('day_of_week', models.CharField(choices=[('MONDAY', 'Monday'), ('TUESDAY', 'Tuesday'), ('WEDNESDAY', 'Wednesday'), ('THURSDAY', 'Thursday'), ('FRIDAY', 'Friday'), ('SATURDAY', 'Saturday'), ('SUNDAY', 'Sunday')], max_length=10, verbose_name='Day of Week')),
                ('start_time', models.TimeField(verbose_name='Start Time')),
                ('end_time', models.TimeField(verbose_name='End Time')),
                ('capacity', models.PositiveIntegerField(default=15, verbose_name='Maximum Capacity')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active Status')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('students', models.ManyToManyField(blank=True, related_name='classes', to='center.student', verbose_name='Enrolled Students')),
                ('teacher', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='classes', to='center.teacher', verbose_name='Assigned Teacher')),
            ],
            options={
                'verbose_name': 'Class',
                'verbose_name_plural': 'Classes',
                'ordering': ['day_of_week', 'start_time'],
            },
        ),
    ]
