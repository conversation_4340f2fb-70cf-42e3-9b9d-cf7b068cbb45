# Generated by Django 4.2.7 on 2025-07-13 19:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('center', '0003_paymentremindersettings_smtp_host_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='class',
            name='days_of_week',
            field=models.CharField(blank=True, help_text='Comma-separated days (e.g., MONDAY,WEDNESDAY,FRIDAY)', max_length=100, verbose_name='Days of Week'),
        ),
        migrations.AlterField(
            model_name='class',
            name='day_of_week',
            field=models.CharField(blank=True, choices=[('MONDAY', 'Monday'), ('TUESDAY', 'Tuesday'), ('WEDNESDAY', 'Wednesday'), ('THURSDAY', 'Thursday'), ('FRIDAY', 'Friday'), ('SATURDAY', 'Saturday'), ('SUNDAY', 'Sunday')], max_length=10, null=True, verbose_name='Day of Week'),
        ),
    ]
