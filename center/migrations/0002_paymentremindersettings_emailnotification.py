# Generated by Django 4.2.7 on 2025-07-13 15:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('center', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentReminderSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reminder_days_before', models.CharField(default='7,3,1', help_text='Comma-separated list of days before due date to send reminders', max_length=50, verbose_name='Reminder Days Before Due')),
                ('overdue_reminder_days', models.CharField(default='1,7,14', help_text='Comma-separated list of days after due date to send overdue reminders', max_length=50, verbose_name='Overdue Reminder Days')),
                ('email_enabled', models.BooleanField(default=True, verbose_name='Email Notifications Enabled')),
                ('from_email', models.EmailField(default='<EMAIL>', max_length=254, verbose_name='From Email Address')),
                ('reply_to_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Reply-To Email Address')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('updated_date', models.DateTimeField(auto_now=True, verbose_name='Updated Date')),
            ],
            options={
                'verbose_name': 'Payment Reminder Settings',
                'verbose_name_plural': 'Payment Reminder Settings',
            },
        ),
        migrations.CreateModel(
            name='EmailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('PAYMENT_REMINDER', 'Payment Reminder'), ('PAYMENT_OVERDUE', 'Payment Overdue'), ('PAYMENT_CONFIRMATION', 'Payment Confirmation'), ('WELCOME', 'Welcome Email'), ('GENERAL', 'General Notification')], max_length=20, verbose_name='Notification Type')),
                ('subject', models.CharField(max_length=200, verbose_name='Email Subject')),
                ('message', models.TextField(verbose_name='Email Message')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('SENT', 'Sent'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=10, verbose_name='Status')),
                ('scheduled_date', models.DateTimeField(verbose_name='Scheduled Send Date')),
                ('sent_date', models.DateTimeField(blank=True, null=True, verbose_name='Actual Send Date')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='Error Message')),
                ('created_date', models.DateTimeField(auto_now_add=True, verbose_name='Created Date')),
                ('payment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='email_notifications', to='center.payment', verbose_name='Related Payment')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_notifications', to='center.student', verbose_name='Student')),
            ],
            options={
                'verbose_name': 'Email Notification',
                'verbose_name_plural': 'Email Notifications',
                'ordering': ['-created_date'],
                'unique_together': {('student', 'payment', 'notification_type', 'scheduled_date')},
            },
        ),
    ]
