# Generated by Django 4.2.7 on 2025-07-13 16:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('center', '0002_paymentremindersettings_emailnotification'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymentremindersettings',
            name='smtp_host',
            field=models.CharField(default='smtp.gmail.com', max_length=255, verbose_name='SMTP Host'),
        ),
        migrations.AddField(
            model_name='paymentremindersettings',
            name='smtp_password',
            field=models.CharField(blank=True, max_length=255, verbose_name='SMTP Password'),
        ),
        migrations.AddField(
            model_name='paymentremindersettings',
            name='smtp_port',
            field=models.PositiveIntegerField(default=587, verbose_name='SMTP Port'),
        ),
        migrations.AddField(
            model_name='paymentremindersettings',
            name='smtp_use_ssl',
            field=models.BooleanField(default=False, verbose_name='Use SSL'),
        ),
        migrations.AddField(
            model_name='paymentremindersettings',
            name='smtp_use_tls',
            field=models.BooleanField(default=True, verbose_name='Use TLS'),
        ),
        migrations.AddField(
            model_name='paymentremindersettings',
            name='smtp_username',
            field=models.CharField(blank=True, max_length=255, verbose_name='SMTP Username'),
        ),
    ]
