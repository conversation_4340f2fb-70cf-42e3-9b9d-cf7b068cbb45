"""
Django signals for automatic email notifications
"""
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Payment
from .email_services import EmailService
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Payment)
def send_payment_confirmation_email(sender, instance, created, **kwargs):
    """
    Send payment confirmation email when a new payment is created and confirmed
    """
    if created and instance.is_confirmed:
        try:
            email_service = EmailService()
            success = email_service.send_payment_confirmation(
                student=instance.student,
                payment=instance
            )
            
            if success:
                logger.info(f"Payment confirmation email sent to {instance.student.email} for payment {instance.id}")
            else:
                logger.warning(f"Failed to send payment confirmation email to {instance.student.email} for payment {instance.id}")
                
        except Exception as e:
            logger.error(f"Error sending payment confirmation email: {str(e)}")


@receiver(post_save, sender=Payment)
def update_payment_confirmation_email(sender, instance, created, **kwargs):
    """
    Send payment confirmation email when an existing payment is confirmed
    """
    if not created and instance.is_confirmed:
        # Check if this payment was just confirmed (not already confirmed)
        try:
            # Get the previous state from database
            old_instance = Payment.objects.get(pk=instance.pk)
            
            # If payment was just confirmed, send confirmation email
            if hasattr(old_instance, '_state') and not old_instance.is_confirmed:
                email_service = EmailService()
                success = email_service.send_payment_confirmation(
                    student=instance.student,
                    payment=instance
                )
                
                if success:
                    logger.info(f"Payment confirmation email sent to {instance.student.email} for updated payment {instance.id}")
                else:
                    logger.warning(f"Failed to send payment confirmation email to {instance.student.email} for updated payment {instance.id}")
                    
        except Payment.DoesNotExist:
            # This shouldn't happen, but handle gracefully
            pass
        except Exception as e:
            logger.error(f"Error sending payment confirmation email for updated payment: {str(e)}")
