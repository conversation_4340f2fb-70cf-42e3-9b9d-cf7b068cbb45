from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone
from datetime import date, timedelta


class Teacher(models.Model):
    """Model for teachers in the language center"""
    name = models.CharField(max_length=100, verbose_name="Full Name")
    email = models.EmailField(unique=True, verbose_name="Email Address")
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    phone_number = models.CharField(validators=[phone_regex], max_length=17, verbose_name="Phone Number")
    subject_specialization = models.Char<PERSON>ield(max_length=100, verbose_name="Subject Specialization")
    date_joined = models.DateTimeField(auto_now_add=True, verbose_name="Date Joined")
    is_active = models.BooleanField(default=True, verbose_name="Active Status")

    class Meta:
        verbose_name = "Teacher"
        verbose_name_plural = "Teachers"
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def active_classes_count(self):
        return self.classes.filter(is_active=True).count()


class Student(models.Model):
    """Model for students in the language center"""
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other'),
    ]

    LEVEL_CHOICES = [
        ('BEGINNER', 'Beginner'),
        ('ELEMENTARY', 'Elementary'),
        ('INTERMEDIATE', 'Intermediate'),
        ('UPPER_INTERMEDIATE', 'Upper-Intermediate'),
        ('ADVANCED', 'Advanced'),
        ('PROFICIENCY', 'Proficiency'),
    ]

    full_name = models.CharField(max_length=100, verbose_name="Full Name")
    email = models.EmailField(unique=True, verbose_name="Email Address")
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    phone_number = models.CharField(validators=[phone_regex], max_length=17, verbose_name="Phone Number")
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, verbose_name="Gender")
    date_of_birth = models.DateField(verbose_name="Date of Birth")
    registration_date = models.DateTimeField(auto_now_add=True, verbose_name="Registration Date")
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES, verbose_name="Current Level")
    is_active = models.BooleanField(default=True, verbose_name="Active Status")

    class Meta:
        verbose_name = "Student"
        verbose_name_plural = "Students"
        ordering = ['full_name']

    def __str__(self):
        return self.full_name

    @property
    def age(self):
        today = date.today()
        return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))

    @property
    def total_paid(self):
        return self.payments.filter(is_confirmed=True).aggregate(
            total=models.Sum('amount')
        )['total'] or 0

    @property
    def pending_payments(self):
        return self.payments.filter(is_confirmed=False).aggregate(
            total=models.Sum('amount')
        )['total'] or 0


class Class(models.Model):
    """Model for classes in the language center"""
    DAYS_OF_WEEK = [
        ('MONDAY', 'Monday'),
        ('TUESDAY', 'Tuesday'),
        ('WEDNESDAY', 'Wednesday'),
        ('THURSDAY', 'Thursday'),
        ('FRIDAY', 'Friday'),
        ('SATURDAY', 'Saturday'),
        ('SUNDAY', 'Sunday'),
    ]

    LEVEL_CHOICES = [
        ('BEGINNER', 'Beginner'),
        ('ELEMENTARY', 'Elementary'),
        ('INTERMEDIATE', 'Intermediate'),
        ('UPPER_INTERMEDIATE', 'Upper-Intermediate'),
        ('ADVANCED', 'Advanced'),
        ('PROFICIENCY', 'Proficiency'),
    ]

    class_name = models.CharField(max_length=100, verbose_name="Class Name")
    level = models.CharField(max_length=20, choices=LEVEL_CHOICES, verbose_name="Level")
    teacher = models.ForeignKey(Teacher, on_delete=models.CASCADE, related_name='classes', verbose_name="Assigned Teacher")
    students = models.ManyToManyField(Student, related_name='classes', blank=True, verbose_name="Enrolled Students")
    day_of_week = models.CharField(max_length=10, choices=DAYS_OF_WEEK, verbose_name="Day of Week")
    start_time = models.TimeField(verbose_name="Start Time")
    end_time = models.TimeField(verbose_name="End Time")
    capacity = models.PositiveIntegerField(default=15, verbose_name="Maximum Capacity")
    is_active = models.BooleanField(default=True, verbose_name="Active Status")
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="Created Date")

    class Meta:
        verbose_name = "Class"
        verbose_name_plural = "Classes"
        ordering = ['day_of_week', 'start_time']

    def __str__(self):
        return f"{self.class_name} - {self.get_level_display()} ({self.get_day_of_week_display()})"

    @property
    def current_enrollment(self):
        return self.students.filter(is_active=True).count()

    @property
    def available_spots(self):
        return self.capacity - self.current_enrollment

    @property
    def is_full(self):
        return self.current_enrollment >= self.capacity

    def schedule_display(self):
        return f"{self.get_day_of_week_display()} {self.start_time.strftime('%H:%M')} - {self.end_time.strftime('%H:%M')}"


class Payment(models.Model):
    """Model for student payments"""
    PAYMENT_METHODS = [
        ('CASH', 'Cash'),
        ('CARD', 'Credit/Debit Card'),
        ('BANK_TRANSFER', 'Bank Transfer'),
        ('ONLINE', 'Online Payment'),
        ('CHECK', 'Check'),
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='payments', verbose_name="Student")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="Amount")
    payment_date = models.DateTimeField(default=timezone.now, verbose_name="Payment Date")
    payment_method = models.CharField(max_length=15, choices=PAYMENT_METHODS, verbose_name="Payment Method")
    next_due_date = models.DateField(verbose_name="Next Due Date")
    is_confirmed = models.BooleanField(default=True, verbose_name="Payment Confirmed")
    notes = models.TextField(blank=True, null=True, verbose_name="Notes")
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="Record Created")

    class Meta:
        verbose_name = "Payment"
        verbose_name_plural = "Payments"
        ordering = ['-payment_date']

    def __str__(self):
        return f"{self.student.full_name} - ${self.amount} ({self.payment_date.strftime('%Y-%m-%d')})"

    @property
    def is_overdue(self):
        return self.next_due_date < date.today() and not self.is_confirmed

    @property
    def days_until_due(self):
        return (self.next_due_date - date.today()).days

    def save(self, *args, **kwargs):
        # Auto-set next due date if not provided (30 days from payment date)
        if not self.next_due_date:
            self.next_due_date = (self.payment_date + timedelta(days=30)).date()
        super().save(*args, **kwargs)


class EmailNotification(models.Model):
    """Model to track email notifications sent to students"""
    NOTIFICATION_TYPES = [
        ('PAYMENT_REMINDER', 'Payment Reminder'),
        ('PAYMENT_OVERDUE', 'Payment Overdue'),
        ('PAYMENT_CONFIRMATION', 'Payment Confirmation'),
        ('WELCOME', 'Welcome Email'),
        ('GENERAL', 'General Notification'),
    ]

    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('SENT', 'Sent'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
    ]

    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name='email_notifications', verbose_name="Student")
    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='email_notifications', null=True, blank=True, verbose_name="Related Payment")
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, verbose_name="Notification Type")
    subject = models.CharField(max_length=200, verbose_name="Email Subject")
    message = models.TextField(verbose_name="Email Message")
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='PENDING', verbose_name="Status")
    scheduled_date = models.DateTimeField(verbose_name="Scheduled Send Date")
    sent_date = models.DateTimeField(null=True, blank=True, verbose_name="Actual Send Date")
    error_message = models.TextField(null=True, blank=True, verbose_name="Error Message")
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="Created Date")

    class Meta:
        verbose_name = "Email Notification"
        verbose_name_plural = "Email Notifications"
        ordering = ['-created_date']
        unique_together = ['student', 'payment', 'notification_type', 'scheduled_date']

    def __str__(self):
        return f"{self.student.full_name} - {self.get_notification_type_display()} ({self.status})"

    @property
    def is_sent(self):
        return self.status == 'SENT'

    @property
    def is_pending(self):
        return self.status == 'PENDING'


class PaymentReminderSettings(models.Model):
    """Model to store payment reminder settings"""
    reminder_days_before = models.CharField(
        max_length=50,
        default='7,3,1',
        help_text="Comma-separated list of days before due date to send reminders",
        verbose_name="Reminder Days Before Due"
    )
    overdue_reminder_days = models.CharField(
        max_length=50,
        default='1,7,14',
        help_text="Comma-separated list of days after due date to send overdue reminders",
        verbose_name="Overdue Reminder Days"
    )
    email_enabled = models.BooleanField(default=True, verbose_name="Email Notifications Enabled")
    from_email = models.EmailField(default='<EMAIL>', verbose_name="From Email Address")
    reply_to_email = models.EmailField(blank=True, null=True, verbose_name="Reply-To Email Address")
    created_date = models.DateTimeField(auto_now_add=True, verbose_name="Created Date")
    updated_date = models.DateTimeField(auto_now=True, verbose_name="Updated Date")

    class Meta:
        verbose_name = "Payment Reminder Settings"
        verbose_name_plural = "Payment Reminder Settings"

    def __str__(self):
        return f"Payment Reminder Settings (Updated: {self.updated_date.strftime('%Y-%m-%d')})"

    @property
    def reminder_days_list(self):
        return [int(day.strip()) for day in self.reminder_days_before.split(',') if day.strip().isdigit()]

    @property
    def overdue_days_list(self):
        return [int(day.strip()) for day in self.overdue_reminder_days.split(',') if day.strip().isdigit()]

    def save(self, *args, **kwargs):
        # Ensure only one settings instance exists
        if not self.pk and PaymentReminderSettings.objects.exists():
            raise ValueError("Only one PaymentReminderSettings instance is allowed")
        super().save(*args, **kwargs)

    @classmethod
    def get_settings(cls):
        """Get the current settings instance, create default if none exists"""
        settings, created = cls.objects.get_or_create(
            pk=1,
            defaults={
                'reminder_days_before': '7,3,1',
                'overdue_reminder_days': '1,7,14',
                'email_enabled': True,
                'from_email': '<EMAIL>'
            }
        )
        return settings
