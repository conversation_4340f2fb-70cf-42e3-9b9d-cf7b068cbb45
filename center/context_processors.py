"""
Context processors for the center app
"""
from datetime import date, timedelta
from .models import Payment, EmailNotification


def notification_context(request):
    """
    Add notification count to all templates
    """
    if not request.user.is_authenticated:
        return {}
    
    try:
        today = date.today()
        
        # Count upcoming due dates (next 7 days)
        upcoming_count = 0
        for days in [1, 3, 7]:
            target_date = today + timedelta(days=days)
            upcoming_count += Payment.objects.filter(
                next_due_date=target_date,
                is_confirmed=True,
                student__is_active=True
            ).count()
        
        # Count overdue payments
        overdue_count = 0
        for days in [1, 3, 7, 14, 30]:
            target_date = today - timedelta(days=days)
            overdue_count += Payment.objects.filter(
                next_due_date=target_date,
                is_confirmed=True,
                student__is_active=True
            ).count()
        
        # Count failed notifications (last 7 days)
        failed_count = EmailNotification.objects.filter(
            status='FAILED',
            scheduled_date__date__gte=today - timedelta(days=7)
        ).count()
        
        # Count students needing reminders
        reminder_count = 0
        for days in [1, 3]:
            target_date = today + timedelta(days=days)
            payments_due = Payment.objects.filter(
                next_due_date=target_date,
                is_confirmed=True,
                student__is_active=True
            )
            
            for payment in payments_due:
                # Check if reminder already sent
                reminder_sent = EmailNotification.objects.filter(
                    student=payment.student,
                    payment=payment,
                    notification_type='PAYMENT_REMINDER',
                    scheduled_date__date=today,
                    status='SENT'
                ).exists()
                
                if not reminder_sent:
                    reminder_count += 1
        
        total_notifications = upcoming_count + overdue_count + failed_count + reminder_count
        
        return {
            'total_notifications': total_notifications,
            'upcoming_notifications': upcoming_count,
            'overdue_notifications': overdue_count,
            'failed_notifications': failed_count,
            'reminder_notifications': reminder_count,
        }
        
    except Exception:
        # If there's any error, return empty context
        return {
            'total_notifications': 0,
            'upcoming_notifications': 0,
            'overdue_notifications': 0,
            'failed_notifications': 0,
            'reminder_notifications': 0,
        }
