from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.utils import timezone
from django.urls import reverse_lazy
from datetime import date, timedelta
from .models import Student, Teacher, Class, Payment
from .forms import StudentForm, TeacherForm, ClassForm, PaymentForm, StudentSearchForm, PaymentSearchForm


@login_required
def dashboard(request):
    """Main dashboard view with system statistics and quick access"""

    # Get current date for calculations
    today = date.today()

    # Basic statistics
    total_students = Student.objects.filter(is_active=True).count()
    total_teachers = Teacher.objects.filter(is_active=True).count()
    total_classes = Class.objects.filter(is_active=True).count()

    # Revenue statistics
    total_revenue = Payment.objects.filter(is_confirmed=True).aggregate(
        total=Sum('amount')
    )['total'] or 0

    monthly_revenue = Payment.objects.filter(
        is_confirmed=True,
        payment_date__month=today.month,
        payment_date__year=today.year
    ).aggregate(total=Sum('amount'))['total'] or 0

    # Payment statistics
    overdue_payments = Payment.objects.filter(
        next_due_date__lt=today,
        is_confirmed=False
    ).count()

    upcoming_payments = Payment.objects.filter(
        next_due_date__gte=today,
        next_due_date__lte=today + timedelta(days=7),
        is_confirmed=False
    ).count()

    # Recent activities
    recent_students = Student.objects.filter(is_active=True).order_by('-registration_date')[:5]
    recent_payments = Payment.objects.filter(is_confirmed=True).order_by('-payment_date')[:5]

    # Class statistics
    classes_by_level = Class.objects.filter(is_active=True).values('level').annotate(
        count=Count('id')
    ).order_by('level')

    # Students by level
    students_by_level = Student.objects.filter(is_active=True).values('level').annotate(
        count=Count('id')
    ).order_by('level')

    context = {
        'total_students': total_students,
        'total_teachers': total_teachers,
        'total_classes': total_classes,
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue,
        'overdue_payments': overdue_payments,
        'upcoming_payments': upcoming_payments,
        'recent_students': recent_students,
        'recent_payments': recent_payments,
        'classes_by_level': classes_by_level,
        'students_by_level': students_by_level,
    }

    return render(request, 'center/dashboard.html', context)


# Student Views
class StudentListView(LoginRequiredMixin, ListView):
    model = Student
    template_name = 'center/student_list.html'
    context_object_name = 'students'
    paginate_by = 12

    def get_queryset(self):
        queryset = Student.objects.filter(is_active=True).order_by('-registration_date')

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(full_name__icontains=search) |
                Q(email__icontains=search) |
                Q(phone_number__icontains=search)
            )

        # Filter by level
        level = self.request.GET.get('level')
        if level:
            queryset = queryset.filter(level=level)

        # Filter by status
        is_active = self.request.GET.get('is_active')
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        elif is_active == 'false':
            queryset = queryset.filter(is_active=False)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = StudentSearchForm(self.request.GET)
        context['total_students'] = Student.objects.filter(is_active=True).count()
        return context


class StudentCreateView(LoginRequiredMixin, CreateView):
    model = Student
    form_class = StudentForm
    template_name = 'center/student_form.html'

    def get_success_url(self):
        messages.success(self.request, f'Student {self.object.full_name} has been created successfully!')
        return reverse_lazy('center:student_list')


class StudentDetailView(LoginRequiredMixin, DetailView):
    model = Student
    template_name = 'center/student_detail.html'
    context_object_name = 'student'


class StudentUpdateView(LoginRequiredMixin, UpdateView):
    model = Student
    form_class = StudentForm
    template_name = 'center/student_form.html'

    def get_success_url(self):
        messages.success(self.request, f'Student {self.object.full_name} has been updated successfully!')
        return reverse_lazy('center:student_detail', kwargs={'pk': self.object.pk})


class StudentDeleteView(LoginRequiredMixin, DeleteView):
    model = Student
    template_name = 'center/student_confirm_delete.html'
    success_url = reverse_lazy('center:student_list')

    def delete(self, request, *args, **kwargs):
        student = self.get_object()
        messages.success(request, f'Student {student.full_name} has been deleted successfully!')
        return super().delete(request, *args, **kwargs)


# Teacher Views
class TeacherListView(LoginRequiredMixin, ListView):
    model = Teacher
    template_name = 'center/teacher_list.html'
    context_object_name = 'teachers'
    paginate_by = 12

    def get_queryset(self):
        return Teacher.objects.filter(is_active=True).order_by('name')


class TeacherCreateView(LoginRequiredMixin, CreateView):
    model = Teacher
    form_class = TeacherForm
    template_name = 'center/teacher_form.html'
    success_url = reverse_lazy('center:teacher_list')


class TeacherDetailView(LoginRequiredMixin, DetailView):
    model = Teacher
    template_name = 'center/teacher_detail.html'
    context_object_name = 'teacher'


class TeacherUpdateView(LoginRequiredMixin, UpdateView):
    model = Teacher
    form_class = TeacherForm
    template_name = 'center/teacher_form.html'
    success_url = reverse_lazy('center:teacher_list')


class TeacherDeleteView(LoginRequiredMixin, DeleteView):
    model = Teacher
    template_name = 'center/teacher_confirm_delete.html'
    success_url = reverse_lazy('center:teacher_list')


# Class Views
class ClassListView(LoginRequiredMixin, ListView):
    model = Class
    template_name = 'center/class_list.html'
    context_object_name = 'classes'
    paginate_by = 12

    def get_queryset(self):
        return Class.objects.filter(is_active=True).order_by('day_of_week', 'start_time')


class ClassCreateView(LoginRequiredMixin, CreateView):
    model = Class
    form_class = ClassForm
    template_name = 'center/class_form.html'
    success_url = reverse_lazy('center:class_list')


class ClassDetailView(LoginRequiredMixin, DetailView):
    model = Class
    template_name = 'center/class_detail.html'
    context_object_name = 'class'


class ClassUpdateView(LoginRequiredMixin, UpdateView):
    model = Class
    form_class = ClassForm
    template_name = 'center/class_form.html'
    success_url = reverse_lazy('center:class_list')


class ClassDeleteView(LoginRequiredMixin, DeleteView):
    model = Class
    template_name = 'center/class_confirm_delete.html'
    success_url = reverse_lazy('center:class_list')


# Payment Views
class PaymentListView(LoginRequiredMixin, ListView):
    model = Payment
    template_name = 'center/payment_list.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        return Payment.objects.all().order_by('-payment_date')


class PaymentCreateView(LoginRequiredMixin, CreateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'center/payment_form.html'
    success_url = reverse_lazy('center:payment_list')


class PaymentDetailView(LoginRequiredMixin, DetailView):
    model = Payment
    template_name = 'center/payment_detail.html'
    context_object_name = 'payment'


class PaymentUpdateView(LoginRequiredMixin, UpdateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'center/payment_form.html'
    success_url = reverse_lazy('center:payment_list')


class PaymentDeleteView(LoginRequiredMixin, DeleteView):
    model = Payment
    template_name = 'center/payment_confirm_delete.html'
    success_url = reverse_lazy('center:payment_list')


@login_required
def settings_view(request):
    """Settings page view"""
    context = {
        'user': request.user,
    }
    return render(request, 'center/settings.html', context)
