from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.contrib import messages
from django.db.models import Sum, Count, Q, Max
from django.utils import timezone
from django.urls import reverse_lazy
from datetime import date, timedelta
from django.contrib.auth.models import User
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
import json
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from .models import Student, Teacher, Class, Payment, PaymentReminderSettings, EmailNotification
from .forms import StudentForm, TeacherForm, ClassForm, PaymentForm, StudentSearchForm, PaymentSearchForm, PaymentReminderSettingsForm
from django.core.mail import send_mail
from django.conf import settings


@login_required
def dashboard(request):
    """Main dashboard view with system statistics and quick access"""

    # Get current date for calculations
    today = date.today()

    # Basic statistics
    total_students = Student.objects.filter(is_active=True).count()
    total_teachers = Teacher.objects.filter(is_active=True).count()
    total_classes = Class.objects.filter(is_active=True).count()

    # Revenue statistics
    total_revenue = Payment.objects.filter(is_confirmed=True).aggregate(
        total=Sum('amount')
    )['total'] or 0

    monthly_revenue = Payment.objects.filter(
        is_confirmed=True,
        payment_date__month=today.month,
        payment_date__year=today.year
    ).aggregate(total=Sum('amount'))['total'] or 0

    # Payment statistics
    overdue_payments = Payment.objects.filter(
        next_due_date__lt=today,
        is_confirmed=False
    ).count()

    upcoming_payments = Payment.objects.filter(
        next_due_date__gte=today,
        next_due_date__lte=today + timedelta(days=7),
        is_confirmed=False
    ).count()

    # Recent activities
    recent_students = Student.objects.filter(is_active=True).order_by('-registration_date')[:5]
    recent_payments = Payment.objects.filter(is_confirmed=True).order_by('-payment_date')[:5]

    # Simple student data for now - we'll add payment info later
    students_with_payments = Student.objects.filter(is_active=True).order_by('-registration_date')[:10]
    recent_registrations = Student.objects.filter(
        is_active=True,
        registration_date__gte=today - timedelta(days=30)
    ).order_by('-registration_date')
    paying_students = Student.objects.filter(is_active=True).order_by('-registration_date')[:10]

    # Class statistics
    classes_by_level = Class.objects.filter(is_active=True).values('level').annotate(
        count=Count('id')
    ).order_by('level')

    # Students by level
    students_by_level = Student.objects.filter(is_active=True).values('level').annotate(
        count=Count('id')
    ).order_by('level')

    # Get individual level counts for charts (using correct uppercase values)
    beginner_count = Student.objects.filter(is_active=True, level='BEGINNER').count()
    elementary_count = Student.objects.filter(is_active=True, level='ELEMENTARY').count()
    intermediate_count = Student.objects.filter(is_active=True, level='INTERMEDIATE').count()
    upper_intermediate_count = Student.objects.filter(is_active=True, level='UPPER_INTERMEDIATE').count()
    advanced_count = Student.objects.filter(is_active=True, level='ADVANCED').count()
    proficiency_count = Student.objects.filter(is_active=True, level='PROFICIENCY').count()

    # Revenue data for the last 6 months
    revenue_data = []
    revenue_labels = []
    for i in range(5, -1, -1):  # Last 6 months
        month_date = today.replace(day=1) - timedelta(days=i*30)
        month_revenue = Payment.objects.filter(
            is_confirmed=True,
            payment_date__month=month_date.month,
            payment_date__year=month_date.year
        ).aggregate(total=Sum('amount'))['total'] or 0
        revenue_data.append(float(month_revenue))
        revenue_labels.append(month_date.strftime('%b'))

    # Active counts for progress bars
    active_students = total_students
    active_teachers = total_teachers

    # Email notification statistics
    today_emails = EmailNotification.objects.filter(scheduled_date__date=today)
    emails_sent_today = today_emails.filter(status='SENT').count()
    emails_failed_today = today_emails.filter(status='FAILED').count()
    emails_pending_today = today_emails.filter(status='PENDING').count()

    # This week's email stats
    week_start = today - timedelta(days=today.weekday())
    week_emails = EmailNotification.objects.filter(
        scheduled_date__date__gte=week_start,
        scheduled_date__date__lte=today
    )
    emails_sent_week = week_emails.filter(status='SENT').count()
    emails_failed_week = week_emails.filter(status='FAILED').count()

    # Payment notification alerts
    # Students with payments due in next 7 days
    upcoming_due_dates = []
    for days in [1, 3, 7]:
        target_date = today + timedelta(days=days)
        upcoming_payments = Payment.objects.filter(
            next_due_date=target_date,
            is_confirmed=True,
            student__is_active=True
        ).select_related('student')

        for payment in upcoming_payments:
            upcoming_due_dates.append({
                'student': payment.student,
                'payment': payment,
                'days_until_due': days,
                'urgency': 'high' if days <= 1 else 'medium' if days <= 3 else 'low'
            })

    # Students with overdue payments
    overdue_payments_list = []
    for days in [1, 3, 7, 14, 30]:
        target_date = today - timedelta(days=days)
        overdue_payments = Payment.objects.filter(
            next_due_date=target_date,
            is_confirmed=True,
            student__is_active=True
        ).select_related('student')

        for payment in overdue_payments:
            overdue_payments_list.append({
                'student': payment.student,
                'payment': payment,
                'days_overdue': days,
                'urgency': 'critical' if days >= 14 else 'high' if days >= 7 else 'medium'
            })

    # Failed email notifications that need attention
    failed_notifications = EmailNotification.objects.filter(
        status='FAILED',
        scheduled_date__date__gte=today - timedelta(days=7)
    ).select_related('student', 'payment')[:10]

    # Students who haven't received reminder emails yet (due in next 3 days)
    students_needing_reminders = []
    for days in [1, 3]:
        target_date = today + timedelta(days=days)
        payments_due = Payment.objects.filter(
            next_due_date=target_date,
            is_confirmed=True,
            student__is_active=True
        ).select_related('student')

        for payment in payments_due:
            # Check if reminder already sent
            reminder_sent = EmailNotification.objects.filter(
                student=payment.student,
                payment=payment,
                notification_type='PAYMENT_REMINDER',
                scheduled_date__date=today,
                status='SENT'
            ).exists()

            if not reminder_sent:
                students_needing_reminders.append({
                    'student': payment.student,
                    'payment': payment,
                    'days_until_due': days
                })

    context = {
        'total_students': total_students,
        'total_teachers': total_teachers,
        'total_classes': total_classes,
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue,
        'overdue_payments': overdue_payments,
        'upcoming_payments': upcoming_payments,
        'recent_students': recent_students,
        'recent_payments': recent_payments,
        'classes_by_level': classes_by_level,
        'students_by_level': students_by_level,
        'beginner_count': beginner_count,
        'elementary_count': elementary_count,
        'intermediate_count': intermediate_count,
        'upper_intermediate_count': upper_intermediate_count,
        'advanced_count': advanced_count,
        'proficiency_count': proficiency_count,
        'active_students': active_students,
        'active_teachers': active_teachers,
        'revenue_data': revenue_data,
        'revenue_labels': revenue_labels,
        'students_with_payments': students_with_payments,
        'recent_registrations': recent_registrations,
        'paying_students': paying_students,
        'emails_sent_today': emails_sent_today,
        'emails_failed_today': emails_failed_today,
        'emails_pending_today': emails_pending_today,
        'emails_sent_week': emails_sent_week,
        'emails_failed_week': emails_failed_week,
        'upcoming_due_dates': upcoming_due_dates,
        'overdue_payments_list': overdue_payments_list,
        'failed_notifications': failed_notifications,
        'students_needing_reminders': students_needing_reminders,
        'total_notifications': len(upcoming_due_dates) + len(overdue_payments_list) + len(failed_notifications) + len(students_needing_reminders),
    }

    return render(request, 'center/dashboard.html', context)


# Student Views
class StudentListView(LoginRequiredMixin, ListView):
    model = Student
    template_name = 'center/student_list.html'
    context_object_name = 'students'
    paginate_by = 12

    def get_queryset(self):
        queryset = Student.objects.filter(is_active=True).order_by('-registration_date')

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(full_name__icontains=search) |
                Q(email__icontains=search) |
                Q(phone_number__icontains=search)
            )

        # Filter by level
        level = self.request.GET.get('level')
        if level:
            queryset = queryset.filter(level=level)

        # Filter by status
        is_active = self.request.GET.get('is_active')
        if is_active == 'true':
            queryset = queryset.filter(is_active=True)
        elif is_active == 'false':
            queryset = queryset.filter(is_active=False)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = StudentSearchForm(self.request.GET)
        context['total_students'] = Student.objects.filter(is_active=True).count()
        return context


class StudentCreateView(LoginRequiredMixin, CreateView):
    model = Student
    form_class = StudentForm
    template_name = 'center/student_form.html'

    def get_success_url(self):
        messages.success(self.request, f'Student {self.object.full_name} has been created successfully!')
        return reverse_lazy('center:student_list')


class StudentDetailView(LoginRequiredMixin, DetailView):
    model = Student
    template_name = 'center/student_detail.html'
    context_object_name = 'student'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add all active classes for enrollment modal
        context['all_classes'] = Class.objects.filter(is_active=True).order_by('start_time', 'class_name')
        return context


class StudentUpdateView(LoginRequiredMixin, UpdateView):
    model = Student
    form_class = StudentForm
    template_name = 'center/student_form.html'

    def get_success_url(self):
        messages.success(self.request, f'Student {self.object.full_name} has been updated successfully!')
        return reverse_lazy('center:student_list')


class StudentDeleteView(LoginRequiredMixin, DeleteView):
    model = Student
    template_name = 'center/student_confirm_delete.html'
    success_url = reverse_lazy('center:student_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        student = self.get_object()

        # Check for related records
        payment_count = student.payment_set.count()
        enrollment_count = student.enrollment_set.count()

        context.update({
            'payment_count': payment_count,
            'enrollment_count': enrollment_count,
            'has_dependencies': payment_count > 0 or enrollment_count > 0,
        })
        return context

    def delete(self, request, *args, **kwargs):
        student = self.get_object()
        student_name = student.full_name

        # Check for dependencies before deletion
        payment_count = student.payment_set.count()
        enrollment_count = student.enrollment_set.count()

        if payment_count > 0 or enrollment_count > 0:
            messages.error(
                request,
                f'Cannot delete {student_name}. Student has {payment_count} payment(s) and {enrollment_count} enrollment(s). '
                'Please remove these records first or contact administrator.'
            )
            return redirect('center:student_detail', pk=student.pk)

        messages.success(request, f'Student {student_name} has been deleted successfully!')
        return super().delete(request, *args, **kwargs)


# Teacher Views
class TeacherListView(LoginRequiredMixin, ListView):
    model = Teacher
    template_name = 'center/teacher_list.html'
    context_object_name = 'teachers'
    paginate_by = 12

    def get_queryset(self):
        return Teacher.objects.filter(is_active=True).order_by('name')


class TeacherCreateView(LoginRequiredMixin, CreateView):
    model = Teacher
    form_class = TeacherForm
    template_name = 'center/teacher_form.html'
    success_url = reverse_lazy('center:teacher_list')


class TeacherDetailView(LoginRequiredMixin, DetailView):
    model = Teacher
    template_name = 'center/teacher_detail.html'
    context_object_name = 'teacher'


class TeacherUpdateView(LoginRequiredMixin, UpdateView):
    model = Teacher
    form_class = TeacherForm
    template_name = 'center/teacher_form.html'
    success_url = reverse_lazy('center:teacher_list')


class TeacherDeleteView(LoginRequiredMixin, DeleteView):
    model = Teacher
    template_name = 'center/teacher_confirm_delete.html'
    success_url = reverse_lazy('center:teacher_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        teacher = self.get_object()

        # Check for related records
        class_count = teacher.class_set.count()

        context.update({
            'class_count': class_count,
            'has_dependencies': class_count > 0,
        })
        return context

    def delete(self, request, *args, **kwargs):
        teacher = self.get_object()
        teacher_name = teacher.full_name

        # Check for dependencies before deletion
        class_count = teacher.class_set.count()

        if class_count > 0:
            messages.error(
                request,
                f'Cannot delete {teacher_name}. Teacher is assigned to {class_count} class(es). '
                'Please reassign or remove these classes first.'
            )
            return redirect('center:teacher_detail', pk=teacher.pk)

        messages.success(request, f'Teacher {teacher_name} has been deleted successfully!')
        return super().delete(request, *args, **kwargs)


# Class Views
class ClassListView(LoginRequiredMixin, ListView):
    model = Class
    template_name = 'center/class_list.html'
    context_object_name = 'classes'
    paginate_by = 12

    def get_queryset(self):
        return Class.objects.filter(is_active=True).order_by('start_time', 'class_name')


class ClassCreateView(LoginRequiredMixin, CreateView):
    model = Class
    form_class = ClassForm
    template_name = 'center/class_form.html'
    success_url = reverse_lazy('center:class_list')


class ClassDetailView(LoginRequiredMixin, DetailView):
    model = Class
    template_name = 'center/class_detail.html'
    context_object_name = 'class'


class ClassUpdateView(LoginRequiredMixin, UpdateView):
    model = Class
    form_class = ClassForm
    template_name = 'center/class_form.html'
    success_url = reverse_lazy('center:class_list')


class ClassDeleteView(LoginRequiredMixin, DeleteView):
    model = Class
    template_name = 'center/class_confirm_delete.html'
    success_url = reverse_lazy('center:class_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        class_obj = self.get_object()

        # Check for related records
        student_count = class_obj.students.filter(is_active=True).count()
        total_students = class_obj.students.count()

        context.update({
            'student_count': student_count,
            'total_students': total_students,
            'has_dependencies': student_count > 0,
        })
        return context

    def delete(self, request, *args, **kwargs):
        class_obj = self.get_object()
        class_name = class_obj.class_name

        # Check for dependencies before deletion
        student_count = class_obj.students.filter(is_active=True).count()

        if student_count > 0:
            messages.error(
                request,
                f'Cannot delete {class_name}. Class has {student_count} active student(s) enrolled. '
                'Please remove all students from this class first or deactivate the class instead.'
            )
            return redirect('center:class_detail', pk=class_obj.pk)

        # Remove all student relationships before deletion
        class_obj.students.clear()

        messages.success(request, f'Class {class_name} has been deleted successfully!')
        return super().delete(request, *args, **kwargs)


# Class Enrollment Views
@login_required
def enroll_student_in_class(request, class_id):
    """Enroll a student in a class"""
    class_obj = get_object_or_404(Class, id=class_id)

    if request.method == 'POST':
        student_id = request.POST.get('student_id')
        if student_id:
            try:
                student = Student.objects.get(id=student_id, is_active=True)

                # Check if student is already enrolled
                if class_obj.students.filter(id=student.id).exists():
                    messages.warning(request, f'{student.full_name} is already enrolled in this class.')
                elif class_obj.is_full:
                    messages.error(request, f'Cannot enroll {student.full_name}. Class is full.')
                else:
                    class_obj.students.add(student)
                    messages.success(request, f'{student.full_name} has been enrolled in {class_obj.class_name}!')

            except Student.DoesNotExist:
                messages.error(request, 'Student not found.')
        else:
            messages.error(request, 'Please select a student.')

    return redirect('center:class_detail', pk=class_id)


@login_required
def remove_student_from_class(request, class_id, student_id):
    """Remove a student from a class"""
    class_obj = get_object_or_404(Class, id=class_id)
    student = get_object_or_404(Student, id=student_id)

    if request.method == 'POST':
        if class_obj.students.filter(id=student.id).exists():
            class_obj.students.remove(student)
            messages.success(request, f'{student.full_name} has been removed from {class_obj.class_name}.')
        else:
            messages.warning(request, f'{student.full_name} is not enrolled in this class.')

    return redirect('center:class_detail', pk=class_id)


@login_required
def get_available_students_for_class(request, class_id):
    """Get available students for enrollment (AJAX endpoint)"""
    class_obj = get_object_or_404(Class, id=class_id)

    # Get students who are not already enrolled in this class
    enrolled_student_ids = class_obj.students.values_list('id', flat=True)
    available_students = Student.objects.filter(
        is_active=True
    ).exclude(
        id__in=enrolled_student_ids
    ).order_by('full_name')

    # Filter by level if specified
    level_filter = request.GET.get('level')
    if level_filter:
        available_students = available_students.filter(level=level_filter)

    # Search by name if specified
    search = request.GET.get('search')
    if search:
        available_students = available_students.filter(
            Q(full_name__icontains=search) |
            Q(email__icontains=search)
        )

    students_data = []
    for student in available_students[:20]:  # Limit to 20 results
        students_data.append({
            'id': student.id,
            'name': student.full_name,
            'level': student.get_level_display(),
            'email': student.email,
            'phone': student.phone_number,
        })

    return JsonResponse({
        'students': students_data,
        'class_level': class_obj.get_level_display(),
        'class_capacity': class_obj.capacity,
        'current_enrollment': class_obj.current_enrollment,
        'available_spots': class_obj.available_spots,
    })


# Payment Views
class PaymentListView(LoginRequiredMixin, ListView):
    model = Payment
    template_name = 'center/payment_list.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        return Payment.objects.all().order_by('-payment_date')


class PaymentCreateView(LoginRequiredMixin, CreateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'center/payment_form.html'
    success_url = reverse_lazy('center:payment_list')


class PaymentDetailView(LoginRequiredMixin, DetailView):
    model = Payment
    template_name = 'center/payment_detail.html'
    context_object_name = 'payment'


class PaymentUpdateView(LoginRequiredMixin, UpdateView):
    model = Payment
    form_class = PaymentForm
    template_name = 'center/payment_form.html'
    success_url = reverse_lazy('center:payment_list')


class PaymentDeleteView(LoginRequiredMixin, DeleteView):
    model = Payment
    template_name = 'center/payment_confirm_delete.html'
    success_url = reverse_lazy('center:payment_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        payment = self.get_object()

        context.update({
            'payment': payment,
            'student_name': payment.student.full_name,
            'amount': payment.amount,
        })
        return context

    def delete(self, request, *args, **kwargs):
        payment = self.get_object()
        student_name = payment.student.full_name
        amount = payment.amount

        messages.success(
            request,
            f'Payment of {amount} MAD for {student_name} has been deleted successfully!'
        )
        return super().delete(request, *args, **kwargs)


@login_required
def settings_view(request):
    """Settings page view with form handling"""
    # Get or create settings instance
    settings = PaymentReminderSettings.get_settings()

    if request.method == 'POST':
        form = PaymentReminderSettingsForm(request.POST, instance=settings)
        if form.is_valid():
            form.save()
            messages.success(request, 'Settings updated successfully!')
            return redirect('center:settings')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = PaymentReminderSettingsForm(instance=settings)

    context = {
        'user': request.user,
        'form': form,
        'settings': settings,
    }
    return render(request, 'center/settings.html', context)


# Student Payment History View
class StudentPaymentHistoryView(LoginRequiredMixin, DetailView):
    model = Student
    template_name = 'center/student_payment_history.html'
    context_object_name = 'student'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        student = self.get_object()
        context['payments'] = Payment.objects.filter(student=student).order_by('-payment_date')
        context['total_paid'] = Payment.objects.filter(student=student, is_confirmed=True).aggregate(
            total=Sum('amount'))['total'] or 0
        context['pending_payments'] = Payment.objects.filter(student=student, is_confirmed=False).aggregate(
            total=Sum('amount'))['total'] or 0
        return context


# Admin Profile Views
class AdminProfileView(LoginRequiredMixin, TemplateView):
    template_name = 'center/admin_profile.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.request.user
        context['total_students'] = Student.objects.count()
        context['total_teachers'] = Teacher.objects.count()
        context['total_classes'] = Class.objects.filter(is_active=True).count()
        context['total_payments'] = Payment.objects.count()
        return context


class AdminProfileUpdateView(LoginRequiredMixin, UpdateView):
    model = User
    fields = ['first_name', 'last_name', 'email']
    template_name = 'center/admin_profile_edit.html'
    success_url = reverse_lazy('center:admin_profile')

    def get_object(self):
        return self.request.user

    def form_valid(self, form):
        messages.success(self.request, 'Profile updated successfully!')
        return super().form_valid(form)


# Settings Update View
class SettingsUpdateView(LoginRequiredMixin, TemplateView):
    template_name = 'center/settings_edit.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.request.user

        # Get or create settings instance
        settings = PaymentReminderSettings.get_settings()
        context['form'] = PaymentReminderSettingsForm(instance=settings)
        context['settings'] = settings

        return context

    def post(self, request, *args, **kwargs):
        # Get or create settings instance
        settings = PaymentReminderSettings.get_settings()
        form = PaymentReminderSettingsForm(request.POST, instance=settings)

        if form.is_valid():
            form.save()
            messages.success(request, 'Settings updated successfully!')
            return redirect('center:settings_edit')
        else:
            messages.error(request, 'Please correct the errors below.')
            context = self.get_context_data(**kwargs)
            context['form'] = form
            return self.render_to_response(context)


@login_required
def test_email_settings(request):
    """Test email configuration"""
    if request.method == 'POST':
        test_email = request.POST.get('test_email')
        if test_email:
            try:
                # Get current settings
                settings_obj = PaymentReminderSettings.get_settings()

                # Send test email
                send_mail(
                    subject='Test Email - English Language Center',
                    message='This is a test email to verify your email configuration is working correctly.\n\nIf you received this email, your SMTP settings are configured properly.',
                    from_email=settings_obj.from_email,
                    recipient_list=[test_email],
                    fail_silently=False
                )

                return JsonResponse({
                    'success': True,
                    'message': f'Test email sent successfully to {test_email}'
                })
            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'message': f'Failed to send test email: {str(e)}'
                })
        else:
            return JsonResponse({
                'success': False,
                'message': 'Please provide a test email address'
            })

    return JsonResponse({
        'success': False,
        'message': 'Invalid request method'
    })


# Excel Export Functions
def create_excel_header_style():
    """Create header style for Excel files"""
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    return header_font, header_fill, header_alignment, border


def create_excel_cell_style():
    """Create cell style for Excel files"""
    cell_alignment = Alignment(horizontal="left", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    return cell_alignment, border


@login_required
def export_dashboard_data(request):
    """Export comprehensive dashboard data to Excel"""

    # Create workbook
    wb = Workbook()

    # Get styles
    header_font, header_fill, header_alignment, border = create_excel_header_style()
    cell_alignment, cell_border = create_excel_cell_style()

    # Remove default sheet
    wb.remove(wb.active)

    # 1. Students Sheet
    ws_students = wb.create_sheet("Students")
    students = Student.objects.filter(is_active=True).order_by('full_name')

    # Headers for students
    student_headers = ['ID', 'Full Name', 'Email', 'Phone', 'Gender', 'Date of Birth', 'Level', 'Registration Date', 'Status']
    for col, header in enumerate(student_headers, 1):
        cell = ws_students.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Student data
    for row, student in enumerate(students, 2):
        ws_students.cell(row=row, column=1, value=student.id).border = cell_border
        ws_students.cell(row=row, column=2, value=student.full_name).border = cell_border
        ws_students.cell(row=row, column=3, value=student.email).border = cell_border
        ws_students.cell(row=row, column=4, value=student.phone_number).border = cell_border
        ws_students.cell(row=row, column=5, value=student.get_gender_display()).border = cell_border
        ws_students.cell(row=row, column=6, value=student.date_of_birth.strftime('%Y-%m-%d')).border = cell_border
        ws_students.cell(row=row, column=7, value=student.get_level_display()).border = cell_border
        ws_students.cell(row=row, column=8, value=student.registration_date.strftime('%Y-%m-%d')).border = cell_border
        ws_students.cell(row=row, column=9, value='Active' if student.is_active else 'Inactive').border = cell_border

    # Auto-adjust column widths for students
    for col in range(1, len(student_headers) + 1):
        ws_students.column_dimensions[get_column_letter(col)].width = 15

    # 2. Teachers Sheet
    ws_teachers = wb.create_sheet("Teachers")
    teachers = Teacher.objects.filter(is_active=True).order_by('name')

    # Headers for teachers
    teacher_headers = ['ID', 'Name', 'Email', 'Phone', 'Subject Specialization', 'Date Joined', 'Active Classes', 'Status']
    for col, header in enumerate(teacher_headers, 1):
        cell = ws_teachers.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Teacher data
    for row, teacher in enumerate(teachers, 2):
        ws_teachers.cell(row=row, column=1, value=teacher.id).border = cell_border
        ws_teachers.cell(row=row, column=2, value=teacher.name).border = cell_border
        ws_teachers.cell(row=row, column=3, value=teacher.email).border = cell_border
        ws_teachers.cell(row=row, column=4, value=teacher.phone_number).border = cell_border
        ws_teachers.cell(row=row, column=5, value=teacher.subject_specialization).border = cell_border
        ws_teachers.cell(row=row, column=6, value=teacher.date_joined.strftime('%Y-%m-%d')).border = cell_border
        ws_teachers.cell(row=row, column=7, value=teacher.active_classes_count).border = cell_border
        ws_teachers.cell(row=row, column=8, value='Active' if teacher.is_active else 'Inactive').border = cell_border

    # Auto-adjust column widths for teachers
    for col in range(1, len(teacher_headers) + 1):
        ws_teachers.column_dimensions[get_column_letter(col)].width = 15

    # 3. Classes Sheet
    ws_classes = wb.create_sheet("Classes")
    classes = Class.objects.filter(is_active=True).order_by('class_name')

    # Headers for classes
    class_headers = ['ID', 'Class Name', 'Level', 'Teacher', 'Day of Week', 'Start Time', 'End Time', 'Capacity', 'Current Enrollment', 'Available Spots', 'Status']
    for col, header in enumerate(class_headers, 1):
        cell = ws_classes.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Class data
    for row, class_obj in enumerate(classes, 2):
        ws_classes.cell(row=row, column=1, value=class_obj.id).border = cell_border
        ws_classes.cell(row=row, column=2, value=class_obj.class_name).border = cell_border
        ws_classes.cell(row=row, column=3, value=class_obj.get_level_display()).border = cell_border
        ws_classes.cell(row=row, column=4, value=class_obj.teacher.name).border = cell_border
        ws_classes.cell(row=row, column=5, value=class_obj.get_days_display()).border = cell_border
        ws_classes.cell(row=row, column=6, value=class_obj.start_time.strftime('%H:%M')).border = cell_border
        ws_classes.cell(row=row, column=7, value=class_obj.end_time.strftime('%H:%M')).border = cell_border
        ws_classes.cell(row=row, column=8, value=class_obj.capacity).border = cell_border
        ws_classes.cell(row=row, column=9, value=class_obj.current_enrollment).border = cell_border
        ws_classes.cell(row=row, column=10, value=class_obj.available_spots).border = cell_border
        ws_classes.cell(row=row, column=11, value='Active' if class_obj.is_active else 'Inactive').border = cell_border

    # Auto-adjust column widths for classes
    for col in range(1, len(class_headers) + 1):
        ws_classes.column_dimensions[get_column_letter(col)].width = 15

    # 4. Payments Sheet
    ws_payments = wb.create_sheet("Payments")
    payments = Payment.objects.all().order_by('-payment_date')[:500]  # Last 500 payments

    # Headers for payments
    payment_headers = ['ID', 'Student', 'Amount', 'Payment Date', 'Payment Method', 'Status', 'Next Due Date', 'Notes']
    for col, header in enumerate(payment_headers, 1):
        cell = ws_payments.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Payment data
    for row, payment in enumerate(payments, 2):
        ws_payments.cell(row=row, column=1, value=payment.id).border = cell_border
        ws_payments.cell(row=row, column=2, value=payment.student.full_name).border = cell_border
        ws_payments.cell(row=row, column=3, value=float(payment.amount)).border = cell_border
        ws_payments.cell(row=row, column=4, value=payment.payment_date.strftime('%Y-%m-%d')).border = cell_border
        ws_payments.cell(row=row, column=5, value=payment.get_payment_method_display()).border = cell_border
        ws_payments.cell(row=row, column=6, value='Confirmed' if payment.is_confirmed else 'Pending').border = cell_border
        ws_payments.cell(row=row, column=7, value=payment.next_due_date.strftime('%Y-%m-%d') if payment.next_due_date else 'N/A').border = cell_border
        ws_payments.cell(row=row, column=8, value=payment.notes or '').border = cell_border

    # Auto-adjust column widths for payments
    for col in range(1, len(payment_headers) + 1):
        ws_payments.column_dimensions[get_column_letter(col)].width = 15

    # 5. Summary Sheet
    ws_summary = wb.create_sheet("Summary")

    # Calculate summary statistics
    today = date.today()
    total_students = Student.objects.filter(is_active=True).count()
    total_teachers = Teacher.objects.filter(is_active=True).count()
    total_classes = Class.objects.filter(is_active=True).count()
    total_revenue = Payment.objects.filter(is_confirmed=True).aggregate(total=Sum('amount'))['total'] or 0
    monthly_revenue = Payment.objects.filter(
        is_confirmed=True,
        payment_date__month=today.month,
        payment_date__year=today.year
    ).aggregate(total=Sum('amount'))['total'] or 0

    # Students by level
    students_by_level = Student.objects.filter(is_active=True).values('level').annotate(count=Count('id'))

    # Summary headers
    summary_data = [
        ['Metric', 'Value'],
        ['Total Active Students', total_students],
        ['Total Active Teachers', total_teachers],
        ['Total Active Classes', total_classes],
        ['Total Revenue', f'${total_revenue:,.2f}'],
        ['Monthly Revenue', f'${monthly_revenue:,.2f}'],
        ['Export Date', today.strftime('%Y-%m-%d')],
        ['', ''],  # Empty row
        ['Students by Level', ''],
    ]

    # Add students by level data
    for level_data in students_by_level:
        level_display = dict(Student.LEVEL_CHOICES).get(level_data['level'], level_data['level'])
        summary_data.append([f'{level_display} Students', level_data['count']])

    # Write summary data
    for row, (metric, value) in enumerate(summary_data, 1):
        cell_metric = ws_summary.cell(row=row, column=1, value=metric)
        cell_value = ws_summary.cell(row=row, column=2, value=value)

        if row == 1 or metric in ['Students by Level']:  # Headers
            cell_metric.font = header_font
            cell_metric.fill = header_fill
            cell_value.font = header_font
            cell_value.fill = header_fill

        cell_metric.border = border
        cell_value.border = border

    # Auto-adjust column widths for summary
    ws_summary.column_dimensions['A'].width = 25
    ws_summary.column_dimensions['B'].width = 20

    # Set summary as active sheet
    wb.active = wb['Summary']

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="language_center_data_{today.strftime("%Y%m%d")}.xlsx"'

    # Save workbook to response
    wb.save(response)
    return response


@login_required
def export_students_excel(request):
    """Export students data to Excel"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Students"

    # Get styles
    header_font, header_fill, header_alignment, border = create_excel_header_style()
    cell_alignment, cell_border = create_excel_cell_style()

    # Headers
    headers = ['ID', 'Full Name', 'Email', 'Phone', 'Gender', 'Date of Birth', 'Level', 'Registration Date', 'Age', 'Total Paid', 'Status']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Data
    students = Student.objects.filter(is_active=True).order_by('full_name')
    for row, student in enumerate(students, 2):
        ws.cell(row=row, column=1, value=student.id).border = cell_border
        ws.cell(row=row, column=2, value=student.full_name).border = cell_border
        ws.cell(row=row, column=3, value=student.email).border = cell_border
        ws.cell(row=row, column=4, value=student.phone_number).border = cell_border
        ws.cell(row=row, column=5, value=student.get_gender_display()).border = cell_border
        ws.cell(row=row, column=6, value=student.date_of_birth.strftime('%Y-%m-%d')).border = cell_border
        ws.cell(row=row, column=7, value=student.get_level_display()).border = cell_border
        ws.cell(row=row, column=8, value=student.registration_date.strftime('%Y-%m-%d')).border = cell_border
        ws.cell(row=row, column=9, value=student.age).border = cell_border
        ws.cell(row=row, column=10, value=float(student.total_paid)).border = cell_border
        ws.cell(row=row, column=11, value='Active' if student.is_active else 'Inactive').border = cell_border

    # Auto-adjust column widths
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 15

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="students_{date.today().strftime("%Y%m%d")}.xlsx"'

    wb.save(response)
    return response


# API Endpoints for Notification Actions

@login_required
@require_POST
def send_payment_reminder_api(request):
    """API endpoint to send individual payment reminder"""
    try:
        data = json.loads(request.body)
        student_id = data.get('student_id')
        payment_id = data.get('payment_id')

        if not student_id or not payment_id:
            return JsonResponse({'success': False, 'error': 'Missing student_id or payment_id'})

        student = get_object_or_404(Student, id=student_id)
        payment = get_object_or_404(Payment, id=payment_id)

        # Import email service
        from .email_services import EmailService
        email_service = EmailService()

        # Calculate days until due
        days_until_due = (payment.next_due_date - date.today()).days

        # Send reminder
        success = email_service.send_payment_reminder(student, payment, days_until_due)

        if success:
            return JsonResponse({
                'success': True,
                'message': f'Payment reminder sent to {student.full_name}'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'Failed to send email'
            })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_POST
def retry_email_notification_api(request):
    """API endpoint to retry failed email notification"""
    try:
        data = json.loads(request.body)
        notification_id = data.get('notification_id')

        if not notification_id:
            return JsonResponse({'success': False, 'error': 'Missing notification_id'})

        notification = get_object_or_404(EmailNotification, id=notification_id)

        # Import email service
        from .email_services import EmailService
        email_service = EmailService()

        # Reset notification status
        notification.status = 'PENDING'
        notification.error_message = None
        notification.save()

        # Retry sending based on notification type
        success = False
        if notification.notification_type == 'PAYMENT_REMINDER':
            days_until_due = (notification.payment.next_due_date - date.today()).days
            success = email_service.send_payment_reminder(
                notification.student,
                notification.payment,
                days_until_due
            )
        elif notification.notification_type == 'PAYMENT_OVERDUE':
            days_overdue = (date.today() - notification.payment.next_due_date).days
            success = email_service.send_overdue_notification(
                notification.student,
                notification.payment,
                days_overdue
            )
        elif notification.notification_type == 'PAYMENT_CONFIRMATION':
            success = email_service.send_payment_confirmation(
                notification.student,
                notification.payment
            )

        if success:
            return JsonResponse({
                'success': True,
                'message': f'Email sent successfully to {notification.student.full_name}'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'Failed to send email'
            })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_POST
def send_all_reminders_api(request):
    """API endpoint to send all pending payment reminders"""
    try:
        # Import email service
        from .email_services import send_daily_payment_reminders

        reminder_count, overdue_count = send_daily_payment_reminders()
        total_sent = reminder_count + overdue_count

        return JsonResponse({
            'success': True,
            'count': total_sent,
            'reminder_count': reminder_count,
            'overdue_count': overdue_count,
            'message': f'Successfully sent {total_sent} notifications'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def export_teachers_excel(request):
    """Export teachers data to Excel"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Teachers"

    # Get styles
    header_font, header_fill, header_alignment, border = create_excel_header_style()
    cell_alignment, cell_border = create_excel_cell_style()

    # Headers
    headers = ['ID', 'Name', 'Email', 'Phone', 'Subject Specialization', 'Date Joined', 'Active Classes Count', 'Status']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Data
    teachers = Teacher.objects.filter(is_active=True).order_by('name')
    for row, teacher in enumerate(teachers, 2):
        ws.cell(row=row, column=1, value=teacher.id).border = cell_border
        ws.cell(row=row, column=2, value=teacher.name).border = cell_border
        ws.cell(row=row, column=3, value=teacher.email).border = cell_border
        ws.cell(row=row, column=4, value=teacher.phone_number).border = cell_border
        ws.cell(row=row, column=5, value=teacher.subject_specialization).border = cell_border
        ws.cell(row=row, column=6, value=teacher.date_joined.strftime('%Y-%m-%d')).border = cell_border
        ws.cell(row=row, column=7, value=teacher.active_classes_count).border = cell_border
        ws.cell(row=row, column=8, value='Active' if teacher.is_active else 'Inactive').border = cell_border

    # Auto-adjust column widths
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 15

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="teachers_{date.today().strftime("%Y%m%d")}.xlsx"'

    wb.save(response)
    return response


@login_required
def export_payments_excel(request):
    """Export payments data to Excel"""
    wb = Workbook()
    ws = wb.active
    ws.title = "Payments"

    # Get styles
    header_font, header_fill, header_alignment, border = create_excel_header_style()
    cell_alignment, cell_border = create_excel_cell_style()

    # Headers
    headers = ['ID', 'Student', 'Amount', 'Payment Date', 'Payment Method', 'Status', 'Next Due Date', 'Notes']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border

    # Data
    payments = Payment.objects.all().order_by('-payment_date')
    for row, payment in enumerate(payments, 2):
        ws.cell(row=row, column=1, value=payment.id).border = cell_border
        ws.cell(row=row, column=2, value=payment.student.full_name).border = cell_border
        ws.cell(row=row, column=3, value=float(payment.amount)).border = cell_border
        ws.cell(row=row, column=4, value=payment.payment_date.strftime('%Y-%m-%d')).border = cell_border
        ws.cell(row=row, column=5, value=payment.get_payment_method_display()).border = cell_border
        ws.cell(row=row, column=6, value='Confirmed' if payment.is_confirmed else 'Pending').border = cell_border
        ws.cell(row=row, column=7, value=payment.next_due_date.strftime('%Y-%m-%d') if payment.next_due_date else 'N/A').border = cell_border
        ws.cell(row=row, column=8, value=payment.notes or '').border = cell_border

    # Auto-adjust column widths
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 15

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="payments_{date.today().strftime("%Y%m%d")}.xlsx"'

    wb.save(response)
    return response
