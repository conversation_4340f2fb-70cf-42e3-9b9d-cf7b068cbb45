from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib import messages
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import date, timedelta
from .models import Student, Teacher, Class, Payment
from .forms import StudentForm, TeacherForm, ClassForm, PaymentForm


@login_required
def dashboard(request):
    """Main dashboard view with system statistics and quick access"""

    # Get current date for calculations
    today = date.today()

    # Basic statistics
    total_students = Student.objects.filter(is_active=True).count()
    total_teachers = Teacher.objects.filter(is_active=True).count()
    total_classes = Class.objects.filter(is_active=True).count()

    # Revenue statistics
    total_revenue = Payment.objects.filter(is_confirmed=True).aggregate(
        total=Sum('amount')
    )['total'] or 0

    monthly_revenue = Payment.objects.filter(
        is_confirmed=True,
        payment_date__month=today.month,
        payment_date__year=today.year
    ).aggregate(total=Sum('amount'))['total'] or 0

    # Payment statistics
    overdue_payments = Payment.objects.filter(
        next_due_date__lt=today,
        is_confirmed=False
    ).count()

    upcoming_payments = Payment.objects.filter(
        next_due_date__gte=today,
        next_due_date__lte=today + timedelta(days=7),
        is_confirmed=False
    ).count()

    # Recent activities
    recent_students = Student.objects.filter(is_active=True).order_by('-registration_date')[:5]
    recent_payments = Payment.objects.filter(is_confirmed=True).order_by('-payment_date')[:5]

    # Class statistics
    classes_by_level = Class.objects.filter(is_active=True).values('level').annotate(
        count=Count('id')
    ).order_by('level')

    # Students by level
    students_by_level = Student.objects.filter(is_active=True).values('level').annotate(
        count=Count('id')
    ).order_by('level')

    context = {
        'total_students': total_students,
        'total_teachers': total_teachers,
        'total_classes': total_classes,
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue,
        'overdue_payments': overdue_payments,
        'upcoming_payments': upcoming_payments,
        'recent_students': recent_students,
        'recent_payments': recent_payments,
        'classes_by_level': classes_by_level,
        'students_by_level': students_by_level,
    }

    return render(request, 'center/dashboard.html', context)
