from django.contrib import admin
from django.utils.html import format_html
from .models import Student, Teacher, Class, Payment


@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone_number', 'subject_specialization', 'active_classes_count', 'is_active', 'date_joined']
    list_filter = ['is_active', 'subject_specialization', 'date_joined']
    search_fields = ['name', 'email', 'phone_number']
    readonly_fields = ['date_joined']
    list_editable = ['is_active']

    fieldsets = (
        ('Personal Information', {
            'fields': ('name', 'email', 'phone_number')
        }),
        ('Professional Information', {
            'fields': ('subject_specialization', 'is_active')
        }),
        ('System Information', {
            'fields': ('date_joined',),
            'classes': ('collapse',)
        }),
    )

    def active_classes_count(self, obj):
        count = obj.active_classes_count
        if count > 0:
            return format_html('<span style="color: green;">{}</span>', count)
        return count
    active_classes_count.short_description = 'Active Classes'


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'email', 'phone_number', 'level', 'age', 'total_paid_display', 'is_active', 'registration_date']
    list_filter = ['level', 'gender', 'is_active', 'registration_date']
    search_fields = ['full_name', 'email', 'phone_number']
    readonly_fields = ['registration_date', 'age']
    list_editable = ['level', 'is_active']
    date_hierarchy = 'registration_date'

    fieldsets = (
        ('Personal Information', {
            'fields': ('full_name', 'email', 'phone_number', 'gender', 'date_of_birth', 'age')
        }),
        ('Academic Information', {
            'fields': ('level', 'is_active')
        }),
        ('System Information', {
            'fields': ('registration_date',),
            'classes': ('collapse',)
        }),
    )

    def total_paid_display(self, obj):
        total = obj.total_paid
        if total > 0:
            return format_html('<span style="color: green;">${:.2f}</span>', total)
        return '$0.00'
    total_paid_display.short_description = 'Total Paid'

    def age(self, obj):
        return obj.age
    age.short_description = 'Age'
