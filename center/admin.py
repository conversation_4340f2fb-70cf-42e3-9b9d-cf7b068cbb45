from django.contrib import admin
from django.utils.html import format_html
from .models import Student, Teacher, Class, Payment


@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone_number', 'subject_specialization', 'active_classes_count', 'is_active', 'date_joined']
    list_filter = ['is_active', 'subject_specialization', 'date_joined']
    search_fields = ['name', 'email', 'phone_number']
    readonly_fields = ['date_joined']
    list_editable = ['is_active']

    fieldsets = (
        ('Personal Information', {
            'fields': ('name', 'email', 'phone_number')
        }),
        ('Professional Information', {
            'fields': ('subject_specialization', 'is_active')
        }),
        ('System Information', {
            'fields': ('date_joined',),
            'classes': ('collapse',)
        }),
    )

    def active_classes_count(self, obj):
        count = obj.active_classes_count
        if count > 0:
            return format_html('<span style="color: green;">{}</span>', count)
        return count
    active_classes_count.short_description = 'Active Classes'


class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0
    readonly_fields = ['created_date', 'is_overdue_display', 'days_until_due']
    fields = ['amount', 'payment_date', 'payment_method', 'next_due_date', 'is_confirmed', 'notes']
    ordering = ['-payment_date']

    def is_overdue_display(self, obj):
        if obj.is_overdue:
            return format_html('<span style="color: red; font-weight: bold;">OVERDUE</span>')
        elif obj.days_until_due <= 7:
            return format_html('<span style="color: orange;">Due Soon</span>')
        return format_html('<span style="color: green;">On Time</span>')
    is_overdue_display.short_description = 'Status'

    def days_until_due(self, obj):
        days = obj.days_until_due
        if days < 0:
            return format_html('<span style="color: red;">{} days overdue</span>', abs(days))
        elif days <= 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return format_html('{} days', days)
    days_until_due.short_description = 'Days Until Due'


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'email', 'phone_number', 'level', 'age', 'total_paid_display', 'pending_payments_display', 'payment_count', 'is_active', 'registration_date']
    list_filter = ['level', 'gender', 'is_active', 'registration_date']
    search_fields = ['full_name', 'email', 'phone_number']
    readonly_fields = ['registration_date', 'age', 'total_paid_display', 'pending_payments_display', 'payment_count', 'last_payment_date']
    list_editable = ['level', 'is_active']
    date_hierarchy = 'registration_date'
    inlines = [PaymentInline]

    fieldsets = (
        ('Personal Information', {
            'fields': ('full_name', 'email', 'phone_number', 'gender', 'date_of_birth', 'age')
        }),
        ('Academic Information', {
            'fields': ('level', 'is_active')
        }),
        ('Payment Summary', {
            'fields': ('total_paid_display', 'pending_payments_display', 'payment_count', 'last_payment_date'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('registration_date',),
            'classes': ('collapse',)
        }),
    )

    def total_paid_display(self, obj):
        total = float(obj.total_paid)
        if total > 0:
            return format_html('<span style="color: green; font-weight: bold;">${:.2f}</span>', total)
        return format_html('<span style="color: gray;">$0.00</span>')
    total_paid_display.short_description = 'Total Paid'

    def pending_payments_display(self, obj):
        pending = float(obj.pending_payments)
        if pending > 0:
            return format_html('<span style="color: orange; font-weight: bold;">${:.2f}</span>', pending)
        return format_html('<span style="color: green;">$0.00</span>')
    pending_payments_display.short_description = 'Pending Payments'

    def payment_count(self, obj):
        count = obj.payments.count()
        if count > 0:
            return format_html('<span style="color: blue;">{} payments</span>', count)
        return format_html('<span style="color: gray;">No payments</span>')
    payment_count.short_description = 'Payment Count'

    def last_payment_date(self, obj):
        last_payment = obj.payments.filter(is_confirmed=True).order_by('-payment_date').first()
        if last_payment:
            return format_html('<span style="color: green;">{}</span>', last_payment.payment_date.strftime('%Y-%m-%d'))
        return format_html('<span style="color: gray;">No payments</span>')
    last_payment_date.short_description = 'Last Payment'



    def age(self, obj):
        return obj.age
    age.short_description = 'Age'


# Register remaining models with basic admin
# admin.site.register(Class)  # Will be registered with custom admin later
# admin.site.register(Payment)  # Will be registered with custom admin later


@admin.register(Class)
class ClassAdmin(admin.ModelAdmin):
    list_display = ['class_name', 'level', 'teacher', 'schedule_display', 'current_enrollment', 'capacity', 'available_spots', 'is_active']
    list_filter = ['level', 'day_of_week', 'teacher', 'is_active', 'created_date']
    search_fields = ['class_name', 'teacher__name']
    readonly_fields = ['created_date', 'current_enrollment', 'available_spots']
    list_editable = ['is_active']
    filter_horizontal = ['students']

    fieldsets = (
        ('Class Information', {
            'fields': ('class_name', 'level', 'teacher')
        }),
        ('Schedule', {
            'fields': ('day_of_week', 'start_time', 'end_time')
        }),
        ('Enrollment', {
            'fields': ('capacity', 'current_enrollment', 'available_spots', 'students')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        }),
    )

    def current_enrollment(self, obj):
        count = obj.current_enrollment
        if count >= obj.capacity:
            return format_html('<span style="color: red; font-weight: bold;">{}</span>', count)
        elif count >= obj.capacity * 0.8:
            return format_html('<span style="color: orange;">{}</span>', count)
        return count
    current_enrollment.short_description = 'Current Enrollment'

    def available_spots(self, obj):
        spots = obj.available_spots
        if spots == 0:
            return format_html('<span style="color: red;">FULL</span>')
        elif spots <= 3:
            return format_html('<span style="color: orange;">{}</span>', spots)
        return spots
    available_spots.short_description = 'Available Spots'


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['student', 'amount', 'payment_date', 'payment_method', 'next_due_date', 'is_confirmed', 'is_overdue_display']
    list_filter = ['payment_method', 'is_confirmed', 'payment_date', 'next_due_date']
    search_fields = ['student__full_name', 'student__email']
    readonly_fields = ['created_date', 'is_overdue_display', 'days_until_due']
    list_editable = ['is_confirmed']
    date_hierarchy = 'payment_date'

    fieldsets = (
        ('Payment Information', {
            'fields': ('student', 'amount', 'payment_method', 'payment_date')
        }),
        ('Due Date Information', {
            'fields': ('next_due_date', 'days_until_due', 'is_overdue_display')
        }),
        ('Status', {
            'fields': ('is_confirmed', 'notes')
        }),
        ('System Information', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        }),
    )

    def is_overdue_display(self, obj):
        if obj.is_overdue:
            return format_html('<span style="color: red; font-weight: bold;">OVERDUE</span>')
        elif obj.days_until_due <= 7:
            return format_html('<span style="color: orange;">Due Soon</span>')
        return format_html('<span style="color: green;">On Time</span>')
    is_overdue_display.short_description = 'Status'

    def days_until_due(self, obj):
        days = obj.days_until_due
        if days < 0:
            return format_html('<span style="color: red;">{} days overdue</span>', abs(days))
        elif days <= 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return format_html('{} days', days)
    days_until_due.short_description = 'Days Until Due'
