from django.contrib import admin
from django.utils.html import format_html
from django.urls import path
from django.shortcuts import render, redirect
from django.contrib import messages
from django.http import JsonResponse
from .models import Student, Teacher, Class, Payment, EmailNotification, PaymentReminderSettings


@admin.register(Teacher)
class TeacherAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone_number', 'subject_specialization', 'active_classes_count', 'is_active', 'date_joined']
    list_filter = ['is_active', 'subject_specialization', 'date_joined']
    search_fields = ['name', 'email', 'phone_number']
    readonly_fields = ['date_joined']
    list_editable = ['is_active']

    fieldsets = (
        ('Personal Information', {
            'fields': ('name', 'email', 'phone_number')
        }),
        ('Professional Information', {
            'fields': ('subject_specialization', 'is_active')
        }),
        ('System Information', {
            'fields': ('date_joined',),
            'classes': ('collapse',)
        }),
    )

    def active_classes_count(self, obj):
        count = obj.active_classes_count
        if count > 0:
            return format_html('<span style="color: green;">{}</span>', count)
        return count
    active_classes_count.short_description = 'Active Classes'


class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0
    readonly_fields = ['created_date', 'is_overdue_display', 'days_until_due']
    fields = ['amount', 'payment_date', 'payment_method', 'next_due_date', 'is_confirmed', 'notes']
    ordering = ['-payment_date']

    def is_overdue_display(self, obj):
        if obj.is_overdue:
            return format_html('<span style="color: red; font-weight: bold;">OVERDUE</span>')
        elif obj.days_until_due <= 7:
            return format_html('<span style="color: orange;">Due Soon</span>')
        return format_html('<span style="color: green;">On Time</span>')
    is_overdue_display.short_description = 'Status'

    def days_until_due(self, obj):
        days = obj.days_until_due
        if days < 0:
            return format_html('<span style="color: red;">{} days overdue</span>', abs(days))
        elif days <= 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return format_html('{} days', days)
    days_until_due.short_description = 'Days Until Due'


@admin.register(Student)
class StudentAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'email', 'phone_number', 'level', 'age', 'total_paid_display', 'pending_payments_display', 'payment_count', 'is_active', 'registration_date']
    list_filter = ['level', 'gender', 'is_active', 'registration_date']
    search_fields = ['full_name', 'email', 'phone_number']
    readonly_fields = ['registration_date', 'age', 'total_paid_display', 'pending_payments_display', 'payment_count', 'last_payment_date']
    list_editable = ['level', 'is_active']
    date_hierarchy = 'registration_date'
    inlines = [PaymentInline]

    fieldsets = (
        ('Personal Information', {
            'fields': ('full_name', 'email', 'phone_number', 'gender', 'date_of_birth', 'age')
        }),
        ('Academic Information', {
            'fields': ('level', 'is_active')
        }),
        ('Payment Summary', {
            'fields': ('total_paid_display', 'pending_payments_display', 'payment_count', 'last_payment_date'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('registration_date',),
            'classes': ('collapse',)
        }),
    )

    def total_paid_display(self, obj):
        try:
            total = obj.total_paid
            if hasattr(total, 'amount'):  # If it's a Decimal or similar
                total = float(total)
            elif isinstance(total, str):  # If it's a string
                total = float(total.replace('$', '').replace(',', ''))
            else:
                total = float(total)

            if total > 0:
                return format_html('<span style="color: green; font-weight: bold;">${}</span>', f'{total:.2f}')
            return format_html('<span style="color: gray;">$0.00</span>')
        except (ValueError, TypeError, AttributeError):
            return format_html('<span style="color: gray;">$0.00</span>')
    total_paid_display.short_description = 'Total Paid'

    def pending_payments_display(self, obj):
        try:
            pending = obj.pending_payments
            if hasattr(pending, 'amount'):
                pending = float(pending)
            elif isinstance(pending, str):
                pending = float(pending.replace('$', '').replace(',', ''))
            else:
                pending = float(pending)

            if pending > 0:
                return format_html('<span style="color: orange; font-weight: bold;">${}</span>', f'{pending:.2f}')
            return format_html('<span style="color: green;">$0.00</span>')
        except (ValueError, TypeError, AttributeError):
            return format_html('<span style="color: green;">$0.00</span>')
    pending_payments_display.short_description = 'Pending Payments'

    def payment_count(self, obj):
        count = obj.payments.count()
        if count > 0:
            return format_html('<span style="color: blue;">{} payments</span>', count)
        return format_html('<span style="color: gray;">No payments</span>')
    payment_count.short_description = 'Payment Count'

    def last_payment_date(self, obj):
        last_payment = obj.payments.filter(is_confirmed=True).order_by('-payment_date').first()
        if last_payment:
            return format_html('<span style="color: green;">{}</span>', last_payment.payment_date.strftime('%Y-%m-%d'))
        return format_html('<span style="color: gray;">No payments</span>')
    last_payment_date.short_description = 'Last Payment'



    def age(self, obj):
        return obj.age
    age.short_description = 'Age'


# Register remaining models with basic admin
# admin.site.register(Class)  # Will be registered with custom admin later
# admin.site.register(Payment)  # Will be registered with custom admin later


@admin.register(Class)
class ClassAdmin(admin.ModelAdmin):
    list_display = ['class_name', 'level', 'teacher', 'schedule_display', 'current_enrollment', 'capacity', 'available_spots', 'is_active']
    list_filter = ['level', 'teacher', 'is_active', 'created_date']
    search_fields = ['class_name', 'teacher__name']
    readonly_fields = ['created_date', 'current_enrollment', 'available_spots']
    list_editable = ['is_active']
    filter_horizontal = ['students']

    fieldsets = (
        ('Class Information', {
            'fields': ('class_name', 'level', 'teacher')
        }),
        ('Schedule', {
            'fields': ('days_of_week', 'start_time', 'end_time'),
            'description': 'Enter days as comma-separated values (e.g., MONDAY,WEDNESDAY,FRIDAY)'
        }),
        ('Enrollment', {
            'fields': ('capacity', 'current_enrollment', 'available_spots', 'students')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        }),
    )

    def current_enrollment(self, obj):
        count = obj.current_enrollment
        if count >= obj.capacity:
            return format_html('<span style="color: red; font-weight: bold;">{}</span>', count)
        elif count >= obj.capacity * 0.8:
            return format_html('<span style="color: orange;">{}</span>', count)
        return count
    current_enrollment.short_description = 'Current Enrollment'

    def available_spots(self, obj):
        spots = obj.available_spots
        if spots == 0:
            return format_html('<span style="color: red;">FULL</span>')
        elif spots <= 3:
            return format_html('<span style="color: orange;">{}</span>', spots)
        return spots
    available_spots.short_description = 'Available Spots'


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['student', 'amount', 'payment_date', 'payment_method', 'next_due_date', 'is_confirmed', 'is_overdue_display']
    list_filter = ['payment_method', 'is_confirmed', 'payment_date', 'next_due_date']
    search_fields = ['student__full_name', 'student__email']
    readonly_fields = ['created_date', 'is_overdue_display', 'days_until_due']
    list_editable = ['is_confirmed']
    date_hierarchy = 'payment_date'

    fieldsets = (
        ('Payment Information', {
            'fields': ('student', 'amount', 'payment_method', 'payment_date')
        }),
        ('Due Date Information', {
            'fields': ('next_due_date', 'days_until_due', 'is_overdue_display')
        }),
        ('Status', {
            'fields': ('is_confirmed', 'notes')
        }),
        ('System Information', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        }),
    )

    def is_overdue_display(self, obj):
        if obj.is_overdue:
            return format_html('<span style="color: red; font-weight: bold;">OVERDUE</span>')
        elif obj.days_until_due <= 7:
            return format_html('<span style="color: orange;">Due Soon</span>')
        return format_html('<span style="color: green;">On Time</span>')
    is_overdue_display.short_description = 'Status'


@admin.register(EmailNotification)
class EmailNotificationAdmin(admin.ModelAdmin):
    list_display = ['student', 'notification_type', 'subject', 'status', 'scheduled_date', 'sent_date']
    list_filter = ['notification_type', 'status', 'scheduled_date', 'sent_date']
    search_fields = ['student__full_name', 'student__email', 'subject']
    readonly_fields = ['created_date', 'sent_date', 'error_message']
    date_hierarchy = 'scheduled_date'
    list_per_page = 50

    fieldsets = (
        ('Notification Details', {
            'fields': ('student', 'payment', 'notification_type', 'subject')
        }),
        ('Content', {
            'fields': ('message',),
            'classes': ('collapse',)
        }),
        ('Status & Timing', {
            'fields': ('status', 'scheduled_date', 'sent_date', 'error_message')
        }),
        ('System Information', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'payment')

    actions = ['resend_failed_notifications', 'mark_as_cancelled']

    def resend_failed_notifications(self, request, queryset):
        """Resend failed notifications"""
        failed_notifications = queryset.filter(status='FAILED')
        count = 0

        for notification in failed_notifications:
            # Reset status to pending for retry
            notification.status = 'PENDING'
            notification.error_message = None
            notification.save()
            count += 1

        self.message_user(
            request,
            f"Reset {count} failed notifications for retry.",
            messages.SUCCESS
        )
    resend_failed_notifications.short_description = "Reset failed notifications for retry"

    def mark_as_cancelled(self, request, queryset):
        """Mark notifications as cancelled"""
        count = queryset.update(status='CANCELLED')
        self.message_user(
            request,
            f"Marked {count} notifications as cancelled.",
            messages.SUCCESS
        )
    mark_as_cancelled.short_description = "Mark as cancelled"


@admin.register(PaymentReminderSettings)
class PaymentReminderSettingsAdmin(admin.ModelAdmin):
    list_display = ['email_enabled', 'from_email', 'smtp_host', 'smtp_port', 'reminder_days_before', 'overdue_reminder_days', 'updated_date']
    readonly_fields = ['created_date', 'updated_date']

    fieldsets = (
        ('Email Configuration', {
            'fields': ('email_enabled', 'from_email', 'reply_to_email')
        }),
        ('SMTP Configuration', {
            'fields': ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_use_tls', 'smtp_use_ssl'),
            'description': 'Configure SMTP server settings for sending emails'
        }),
        ('Reminder Settings', {
            'fields': ('reminder_days_before', 'overdue_reminder_days'),
            'description': 'Enter comma-separated numbers for days before/after due date to send reminders'
        }),
        ('System Information', {
            'fields': ('created_date', 'updated_date'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        # Only allow one settings instance
        return not PaymentReminderSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('send-test-email/', self.admin_site.admin_view(self.send_test_email), name='send_test_email'),
            path('send-manual-reminders/', self.admin_site.admin_view(self.send_manual_reminders), name='send_manual_reminders'),
        ]
        return custom_urls + urls

    def send_test_email(self, request):
        """Send a test email to verify email configuration"""
        if request.method == 'POST':
            test_email = request.POST.get('test_email')
            if test_email:
                try:
                    from django.core.mail import send_mail
                    from django.conf import settings

                    send_mail(
                        subject='Test Email - English Language Center',
                        message='This is a test email to verify your email configuration is working correctly.',
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[test_email],
                        fail_silently=False
                    )

                    messages.success(request, f'Test email sent successfully to {test_email}')
                except Exception as e:
                    messages.error(request, f'Failed to send test email: {str(e)}')
            else:
                messages.error(request, 'Please provide a test email address')

            return redirect('..')

        context = {
            'title': 'Send Test Email',
            'opts': self.model._meta,
        }
        return render(request, 'admin/center/send_test_email.html', context)

    def send_manual_reminders(self, request):
        """Manually trigger payment reminders"""
        if request.method == 'POST':
            try:
                from center.email_services import send_daily_payment_reminders
                reminder_count, overdue_count = send_daily_payment_reminders()

                total_sent = reminder_count + overdue_count
                if total_sent > 0:
                    messages.success(
                        request,
                        f'Successfully sent {reminder_count} payment reminders and {overdue_count} overdue notifications'
                    )
                else:
                    messages.info(request, 'No payment reminders needed to be sent at this time')

            except Exception as e:
                messages.error(request, f'Error sending reminders: {str(e)}')

            return redirect('..')

        # Get preview of what would be sent
        from center.email_services import EmailService
        email_service = EmailService()

        students_to_remind = email_service.get_students_needing_reminders()
        students_overdue = email_service.get_students_with_overdue_payments()

        context = {
            'title': 'Send Manual Payment Reminders',
            'opts': self.model._meta,
            'students_to_remind': students_to_remind,
            'students_overdue': students_overdue,
            'total_emails': len(students_to_remind) + len(students_overdue),
        }
        return render(request, 'admin/center/send_manual_reminders.html', context)

    def days_until_due(self, obj):
        days = obj.days_until_due
        if days < 0:
            return format_html('<span style="color: red;">{} days overdue</span>', abs(days))
        elif days <= 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return format_html('{} days', days)
    days_until_due.short_description = 'Days Until Due'
