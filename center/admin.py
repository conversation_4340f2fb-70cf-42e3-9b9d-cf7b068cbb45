from django.contrib import admin
from django.contrib.admin import AdminSite
from django.utils.html import format_html
from django.urls import path, reverse
from django.shortcuts import render, redirect
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import date, timedelta
import csv
from .models import Student, Teacher, Class, Payment, EmailNotification, PaymentReminderSettings


class LanguageCenterAdminSite(AdminSite):
    """Custom Admin Site for Language Center Management"""
    site_header = 'Language Center Administration'
    site_title = 'Language Center Admin'
    index_title = 'Welcome to Language Center Administration'

    def index(self, request, extra_context=None):
        """Enhanced admin index with statistics"""
        extra_context = extra_context or {}

        # Get statistics
        today = date.today()

        # Basic counts
        total_students = Student.objects.filter(is_active=True).count()
        total_teachers = Teacher.objects.filter(is_active=True).count()
        total_classes = Class.objects.filter(is_active=True).count()

        # Revenue statistics
        total_revenue = Payment.objects.filter(is_confirmed=True).aggregate(
            total=Sum('amount')
        )['total'] or 0

        monthly_revenue = Payment.objects.filter(
            is_confirmed=True,
            payment_date__month=today.month,
            payment_date__year=today.year
        ).aggregate(total=Sum('amount'))['total'] or 0

        # Payment alerts
        overdue_payments = Payment.objects.filter(
            next_due_date__lt=today,
            is_confirmed=False
        ).count()

        upcoming_payments = Payment.objects.filter(
            next_due_date__gte=today,
            next_due_date__lte=today + timedelta(days=7),
            is_confirmed=False
        ).count()

        # Recent activities
        recent_students = Student.objects.filter(is_active=True).order_by('-registration_date')[:5]
        recent_payments = Payment.objects.filter(is_confirmed=True).order_by('-payment_date')[:5]

        # Email notifications
        failed_emails = EmailNotification.objects.filter(
            status='FAILED',
            scheduled_date__date__gte=today - timedelta(days=7)
        ).count()

        extra_context.update({
            'total_students': total_students,
            'total_teachers': total_teachers,
            'total_classes': total_classes,
            'total_revenue': total_revenue,
            'monthly_revenue': monthly_revenue,
            'overdue_payments': overdue_payments,
            'upcoming_payments': upcoming_payments,
            'recent_students': recent_students,
            'recent_payments': recent_payments,
            'failed_emails': failed_emails,
        })

        return super().index(request, extra_context)


# Create custom admin site instance
admin_site = LanguageCenterAdminSite(name='language_center_admin')


class TeacherAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone_number', 'subject_specialization', 'active_classes_count', 'total_students_count', 'is_active', 'date_joined']
    list_filter = ['is_active', 'subject_specialization', 'date_joined']
    search_fields = ['name', 'email', 'phone_number', 'subject_specialization']
    readonly_fields = ['date_joined', 'active_classes_count', 'total_students_count']
    list_editable = ['is_active']
    list_per_page = 20

    # Custom actions
    actions = ['export_teachers_csv', 'activate_teachers', 'deactivate_teachers']

    fieldsets = (
        ('Personal Information', {
            'fields': ('name', 'email', 'phone_number')
        }),
        ('Professional Information', {
            'fields': ('subject_specialization', 'is_active')
        }),
        ('System Information', {
            'fields': ('date_joined',),
            'classes': ('collapse',)
        }),
    )

    def active_classes_count(self, obj):
        count = obj.classes.filter(is_active=True).count()
        if count > 0:
            return format_html('<span style="color: green; font-weight: bold;">{} classes</span>', count)
        return format_html('<span style="color: gray;">No classes</span>')
    active_classes_count.short_description = 'Active Classes'

    def total_students_count(self, obj):
        """Count total students across all teacher's classes"""
        total = 0
        for class_obj in obj.classes.filter(is_active=True):
            total += class_obj.current_enrollment

        if total > 0:
            return format_html('<span style="color: blue; font-weight: bold;">{} students</span>', total)
        return format_html('<span style="color: gray;">No students</span>')
    total_students_count.short_description = 'Total Students'

    # Custom Actions
    def export_teachers_csv(self, request, queryset):
        """Export selected teachers to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="teachers_export.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Name', 'Email', 'Phone', 'Specialization', 'Active Classes',
            'Total Students', 'Date Joined', 'Status'
        ])

        for teacher in queryset:
            total_students = sum(cls.current_enrollment for cls in teacher.classes.filter(is_active=True))
            writer.writerow([
                teacher.name,
                teacher.email,
                teacher.phone_number,
                teacher.subject_specialization,
                teacher.classes.filter(is_active=True).count(),
                total_students,
                teacher.date_joined.strftime('%Y-%m-%d'),
                'Active' if teacher.is_active else 'Inactive'
            ])

        self.message_user(request, f"Exported {queryset.count()} teachers to CSV.")
        return response
    export_teachers_csv.short_description = "Export selected teachers to CSV"

    def activate_teachers(self, request, queryset):
        """Activate selected teachers"""
        count = queryset.update(is_active=True)
        self.message_user(request, f"Activated {count} teachers.")
    activate_teachers.short_description = "Activate selected teachers"

    def deactivate_teachers(self, request, queryset):
        """Deactivate selected teachers"""
        count = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {count} teachers.")
    deactivate_teachers.short_description = "Deactivate selected teachers"


class PaymentInline(admin.TabularInline):
    model = Payment
    extra = 0
    readonly_fields = ['created_date', 'is_overdue_display', 'days_until_due']
    fields = ['amount', 'payment_date', 'payment_method', 'next_due_date', 'is_confirmed', 'notes']
    ordering = ['-payment_date']

    def is_overdue_display(self, obj):
        if obj.is_overdue:
            return format_html('<span style="color: red; font-weight: bold;">OVERDUE</span>')
        elif obj.days_until_due <= 7:
            return format_html('<span style="color: orange;">Due Soon</span>')
        return format_html('<span style="color: green;">On Time</span>')
    is_overdue_display.short_description = 'Status'

    def days_until_due(self, obj):
        days = obj.days_until_due
        if days < 0:
            return format_html('<span style="color: red;">{} days overdue</span>', abs(days))
        elif days <= 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return format_html('{} days', days)
    days_until_due.short_description = 'Days Until Due'


class StudentAdmin(admin.ModelAdmin):
    list_display = ['full_name', 'email', 'phone_number', 'level', 'age', 'total_paid_display', 'pending_payments_display', 'payment_count', 'enrolled_classes_count', 'is_active', 'registration_date']
    list_filter = ['level', 'gender', 'is_active', 'registration_date', 'classes__level']
    search_fields = ['full_name', 'email', 'phone_number']
    readonly_fields = ['registration_date', 'age', 'total_paid_display', 'pending_payments_display', 'payment_count', 'last_payment_date', 'enrolled_classes_count']
    list_editable = ['level', 'is_active']
    date_hierarchy = 'registration_date'
    inlines = [PaymentInline]
    list_per_page = 25
    list_max_show_all = 100

    # Advanced filtering
    list_filter = [
        'level',
        'gender',
        'is_active',
        'registration_date',
        ('date_of_birth', admin.DateFieldListFilter),
        ('classes', admin.RelatedOnlyFieldListFilter),
    ]

    # Custom actions
    actions = ['export_students_csv', 'send_payment_reminders', 'activate_students', 'deactivate_students']

    fieldsets = (
        ('Personal Information', {
            'fields': ('full_name', 'email', 'phone_number', 'gender', 'date_of_birth', 'age')
        }),
        ('Academic Information', {
            'fields': ('level', 'is_active')
        }),
        ('Payment Summary', {
            'fields': ('total_paid_display', 'pending_payments_display', 'payment_count', 'last_payment_date'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('registration_date',),
            'classes': ('collapse',)
        }),
    )

    def enrolled_classes_count(self, obj):
        count = obj.classes.filter(is_active=True).count()
        if count > 0:
            return format_html('<span style="color: blue; font-weight: bold;">{} classes</span>', count)
        return format_html('<span style="color: gray;">No classes</span>')
    enrolled_classes_count.short_description = 'Enrolled Classes'

    def total_paid_display(self, obj):
        try:
            total = obj.total_paid
            if hasattr(total, 'amount'):  # If it's a Decimal or similar
                total = float(total)
            elif isinstance(total, str):  # If it's a string
                total = float(total.replace('MAD', '').replace(',', ''))
            else:
                total = float(total)

            if total > 0:
                return format_html('<span style="color: green; font-weight: bold;">{} MAD</span>', f'{total:.2f}')
            return format_html('<span style="color: gray;">0.00 MAD</span>')
        except (ValueError, TypeError, AttributeError):
            return format_html('<span style="color: gray;">0.00 MAD</span>')
    total_paid_display.short_description = 'Total Paid'

    def pending_payments_display(self, obj):
        try:
            pending = obj.pending_payments
            if hasattr(pending, 'amount'):
                pending = float(pending)
            elif isinstance(pending, str):
                pending = float(pending.replace('MAD', '').replace(',', ''))
            else:
                pending = float(pending)

            if pending > 0:
                return format_html('<span style="color: orange; font-weight: bold;">{} MAD</span>', f'{pending:.2f}')
            return format_html('<span style="color: green;">0.00 MAD</span>')
        except (ValueError, TypeError, AttributeError):
            return format_html('<span style="color: green;">0.00 MAD</span>')
    pending_payments_display.short_description = 'Pending Payments'

    def payment_count(self, obj):
        count = obj.payments.count()
        if count > 0:
            return format_html('<span style="color: blue;">{} payments</span>', count)
        return format_html('<span style="color: gray;">No payments</span>')
    payment_count.short_description = 'Payment Count'

    def last_payment_date(self, obj):
        last_payment = obj.payments.filter(is_confirmed=True).order_by('-payment_date').first()
        if last_payment:
            return format_html('<span style="color: green;">{}</span>', last_payment.payment_date.strftime('%Y-%m-%d'))
        return format_html('<span style="color: gray;">No payments</span>')
    last_payment_date.short_description = 'Last Payment'



    def age(self, obj):
        return obj.age
    age.short_description = 'Age'

    # Custom Actions
    def export_students_csv(self, request, queryset):
        """Export selected students to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="students_export.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Full Name', 'Email', 'Phone', 'Level', 'Gender', 'Age',
            'Registration Date', 'Total Paid', 'Pending Payments', 'Classes Count', 'Status'
        ])

        for student in queryset:
            writer.writerow([
                student.full_name,
                student.email,
                student.phone_number,
                student.get_level_display(),
                student.get_gender_display(),
                student.age,
                student.registration_date.strftime('%Y-%m-%d'),
                f'{student.total_paid:.2f}',
                f'{student.pending_payments:.2f}',
                student.classes.count(),
                'Active' if student.is_active else 'Inactive'
            ])

        self.message_user(request, f"Exported {queryset.count()} students to CSV.")
        return response
    export_students_csv.short_description = "Export selected students to CSV"

    def send_payment_reminders(self, request, queryset):
        """Send payment reminders to selected students"""
        count = 0
        for student in queryset:
            # Check if student has pending payments
            pending_payments = student.payments.filter(
                is_confirmed=False,
                next_due_date__lte=date.today() + timedelta(days=7)
            )
            if pending_payments.exists():
                # Logic to send reminder would go here
                count += 1

        self.message_user(request, f"Payment reminders sent to {count} students.")
    send_payment_reminders.short_description = "Send payment reminders"

    def activate_students(self, request, queryset):
        """Activate selected students"""
        count = queryset.update(is_active=True)
        self.message_user(request, f"Activated {count} students.")
    activate_students.short_description = "Activate selected students"

    def deactivate_students(self, request, queryset):
        """Deactivate selected students"""
        count = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {count} students.")
    deactivate_students.short_description = "Deactivate selected students"


# Register remaining models with basic admin
# admin.site.register(Class)  # Will be registered with custom admin later
# admin.site.register(Payment)  # Will be registered with custom admin later


class ClassAdmin(admin.ModelAdmin):
    list_display = ['class_name', 'level', 'teacher', 'schedule_display', 'current_enrollment', 'capacity', 'available_spots', 'sessions_per_week', 'is_active']
    list_filter = ['level', 'teacher', 'is_active', 'created_date', 'start_time']
    search_fields = ['class_name', 'teacher__name', 'students__full_name']
    readonly_fields = ['created_date', 'current_enrollment', 'available_spots', 'sessions_per_week']
    list_editable = ['is_active']
    filter_horizontal = ['students']
    list_per_page = 20

    # Custom actions
    actions = ['export_classes_csv', 'activate_classes', 'deactivate_classes', 'duplicate_classes']

    fieldsets = (
        ('Class Information', {
            'fields': ('class_name', 'level', 'teacher')
        }),
        ('Schedule', {
            'fields': ('days_of_week', 'start_time', 'end_time'),
            'description': 'Enter days as comma-separated values (e.g., MONDAY,WEDNESDAY,FRIDAY)'
        }),
        ('Enrollment', {
            'fields': ('capacity', 'current_enrollment', 'available_spots', 'students')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('System Information', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        }),
    )

    def current_enrollment(self, obj):
        count = obj.current_enrollment
        if count >= obj.capacity:
            return format_html('<span style="color: red; font-weight: bold;">{}</span>', count)
        elif count >= obj.capacity * 0.8:
            return format_html('<span style="color: orange;">{}</span>', count)
        return count
    current_enrollment.short_description = 'Current Enrollment'

    def available_spots(self, obj):
        spots = obj.available_spots
        if spots == 0:
            return format_html('<span style="color: red; font-weight: bold;">FULL</span>')
        elif spots <= 3:
            return format_html('<span style="color: orange; font-weight: bold;">{}</span>', spots)
        return format_html('<span style="color: green;">{}</span>', spots)
    available_spots.short_description = 'Available Spots'

    def sessions_per_week(self, obj):
        sessions = obj.sessions_per_week
        if sessions > 3:
            return format_html('<span style="color: blue; font-weight: bold;">{} sessions</span>', sessions)
        return format_html('{} session{}', sessions, 's' if sessions != 1 else '')
    sessions_per_week.short_description = 'Sessions/Week'

    # Custom Actions
    def export_classes_csv(self, request, queryset):
        """Export selected classes to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="classes_export.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Class Name', 'Level', 'Teacher', 'Schedule', 'Current Enrollment',
            'Capacity', 'Available Spots', 'Sessions per Week', 'Status'
        ])

        for class_obj in queryset:
            writer.writerow([
                class_obj.class_name,
                class_obj.get_level_display(),
                class_obj.teacher.name,
                class_obj.schedule_display(),
                class_obj.current_enrollment,
                class_obj.capacity,
                class_obj.available_spots,
                class_obj.sessions_per_week,
                'Active' if class_obj.is_active else 'Inactive'
            ])

        self.message_user(request, f"Exported {queryset.count()} classes to CSV.")
        return response
    export_classes_csv.short_description = "Export selected classes to CSV"

    def activate_classes(self, request, queryset):
        """Activate selected classes"""
        count = queryset.update(is_active=True)
        self.message_user(request, f"Activated {count} classes.")
    activate_classes.short_description = "Activate selected classes"

    def deactivate_classes(self, request, queryset):
        """Deactivate selected classes"""
        count = queryset.update(is_active=False)
        self.message_user(request, f"Deactivated {count} classes.")
    deactivate_classes.short_description = "Deactivate selected classes"

    def duplicate_classes(self, request, queryset):
        """Duplicate selected classes"""
        count = 0
        for class_obj in queryset:
            # Create a copy of the class
            new_class = Class.objects.create(
                class_name=f"{class_obj.class_name} (Copy)",
                level=class_obj.level,
                teacher=class_obj.teacher,
                days_of_week=class_obj.days_of_week,
                start_time=class_obj.start_time,
                end_time=class_obj.end_time,
                capacity=class_obj.capacity,
                is_active=False  # Start as inactive
            )
            count += 1

        self.message_user(request, f"Created {count} duplicate classes (inactive).")
    duplicate_classes.short_description = "Duplicate selected classes"


class PaymentAdmin(admin.ModelAdmin):
    list_display = ['student', 'amount_mad', 'payment_date', 'payment_method', 'next_due_date', 'is_confirmed', 'is_overdue_display', 'days_until_due_display']
    list_filter = ['payment_method', 'is_confirmed', 'payment_date', 'next_due_date', 'student__level']
    search_fields = ['student__full_name', 'student__email', 'notes']
    readonly_fields = ['created_date', 'is_overdue_display', 'days_until_due', 'days_until_due_display']
    list_editable = ['is_confirmed']
    date_hierarchy = 'payment_date'
    list_per_page = 30

    # Custom actions
    actions = ['export_payments_csv', 'confirm_payments', 'send_payment_confirmations', 'mark_overdue']

    fieldsets = (
        ('Payment Information', {
            'fields': ('student', 'amount', 'payment_method', 'payment_date')
        }),
        ('Due Date Information', {
            'fields': ('next_due_date', 'days_until_due', 'is_overdue_display')
        }),
        ('Status', {
            'fields': ('is_confirmed', 'notes')
        }),
        ('System Information', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        }),
    )

    def amount_mad(self, obj):
        """Display amount in MAD currency"""
        return format_html('<span style="font-weight: bold;">{} MAD</span>', f'{obj.amount:.2f}')
    amount_mad.short_description = 'Amount (MAD)'

    def is_overdue_display(self, obj):
        if obj.is_overdue:
            return format_html('<span style="color: red; font-weight: bold;">OVERDUE</span>')
        elif obj.days_until_due <= 7:
            return format_html('<span style="color: orange; font-weight: bold;">Due Soon</span>')
        return format_html('<span style="color: green;">On Time</span>')
    is_overdue_display.short_description = 'Status'

    def days_until_due_display(self, obj):
        days = obj.days_until_due
        if days < 0:
            return format_html('<span style="color: red; font-weight: bold;">{} days overdue</span>', abs(days))
        elif days <= 7:
            return format_html('<span style="color: orange; font-weight: bold;">{} days</span>', days)
        return format_html('<span style="color: green;">{} days</span>', days)
    days_until_due_display.short_description = 'Days Until Due'

    # Custom Actions
    def export_payments_csv(self, request, queryset):
        """Export selected payments to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="payments_export.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Student', 'Amount (MAD)', 'Payment Date', 'Payment Method',
            'Next Due Date', 'Status', 'Days Until Due', 'Notes'
        ])

        for payment in queryset:
            writer.writerow([
                payment.student.full_name,
                f'{payment.amount:.2f}',
                payment.payment_date.strftime('%Y-%m-%d'),
                payment.get_payment_method_display(),
                payment.next_due_date.strftime('%Y-%m-%d'),
                'Confirmed' if payment.is_confirmed else 'Pending',
                payment.days_until_due,
                payment.notes or ''
            ])

        self.message_user(request, f"Exported {queryset.count()} payments to CSV.")
        return response
    export_payments_csv.short_description = "Export selected payments to CSV"

    def confirm_payments(self, request, queryset):
        """Confirm selected payments"""
        count = queryset.update(is_confirmed=True)
        self.message_user(request, f"Confirmed {count} payments.")
    confirm_payments.short_description = "Confirm selected payments"

    def send_payment_confirmations(self, request, queryset):
        """Send payment confirmation emails"""
        count = 0
        for payment in queryset.filter(is_confirmed=True):
            # Logic to send confirmation email would go here
            count += 1

        self.message_user(request, f"Sent confirmation emails for {count} payments.")
    send_payment_confirmations.short_description = "Send payment confirmations"

    def mark_overdue(self, request, queryset):
        """Mark payments as overdue (for testing)"""
        from datetime import date, timedelta
        yesterday = date.today() - timedelta(days=1)
        count = queryset.update(next_due_date=yesterday, is_confirmed=False)
        self.message_user(request, f"Marked {count} payments as overdue.")
    mark_overdue.short_description = "Mark as overdue (testing)"


@admin.register(EmailNotification)
class EmailNotificationAdmin(admin.ModelAdmin):
    list_display = ['student', 'notification_type', 'subject', 'status', 'scheduled_date', 'sent_date']
    list_filter = ['notification_type', 'status', 'scheduled_date', 'sent_date']
    search_fields = ['student__full_name', 'student__email', 'subject']
    readonly_fields = ['created_date', 'sent_date', 'error_message']
    date_hierarchy = 'scheduled_date'
    list_per_page = 50

    fieldsets = (
        ('Notification Details', {
            'fields': ('student', 'payment', 'notification_type', 'subject')
        }),
        ('Content', {
            'fields': ('message',),
            'classes': ('collapse',)
        }),
        ('Status & Timing', {
            'fields': ('status', 'scheduled_date', 'sent_date', 'error_message')
        }),
        ('System Information', {
            'fields': ('created_date',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('student', 'payment')

    actions = ['resend_failed_notifications', 'mark_as_cancelled']

    def resend_failed_notifications(self, request, queryset):
        """Resend failed notifications"""
        failed_notifications = queryset.filter(status='FAILED')
        count = 0

        for notification in failed_notifications:
            # Reset status to pending for retry
            notification.status = 'PENDING'
            notification.error_message = None
            notification.save()
            count += 1

        self.message_user(
            request,
            f"Reset {count} failed notifications for retry.",
            messages.SUCCESS
        )
    resend_failed_notifications.short_description = "Reset failed notifications for retry"

    def mark_as_cancelled(self, request, queryset):
        """Mark notifications as cancelled"""
        count = queryset.update(status='CANCELLED')
        self.message_user(
            request,
            f"Marked {count} notifications as cancelled.",
            messages.SUCCESS
        )
    mark_as_cancelled.short_description = "Mark as cancelled"


@admin.register(PaymentReminderSettings)
class PaymentReminderSettingsAdmin(admin.ModelAdmin):
    list_display = ['email_enabled', 'from_email', 'smtp_host', 'smtp_port', 'reminder_days_before', 'overdue_reminder_days', 'updated_date']
    readonly_fields = ['created_date', 'updated_date']

    fieldsets = (
        ('Email Configuration', {
            'fields': ('email_enabled', 'from_email', 'reply_to_email')
        }),
        ('SMTP Configuration', {
            'fields': ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_use_tls', 'smtp_use_ssl'),
            'description': 'Configure SMTP server settings for sending emails'
        }),
        ('Reminder Settings', {
            'fields': ('reminder_days_before', 'overdue_reminder_days'),
            'description': 'Enter comma-separated numbers for days before/after due date to send reminders'
        }),
        ('System Information', {
            'fields': ('created_date', 'updated_date'),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        # Only allow one settings instance
        return not PaymentReminderSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of settings
        return False

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('send-test-email/', self.admin_site.admin_view(self.send_test_email), name='send_test_email'),
            path('send-manual-reminders/', self.admin_site.admin_view(self.send_manual_reminders), name='send_manual_reminders'),
        ]
        return custom_urls + urls

    def send_test_email(self, request):
        """Send a test email to verify email configuration"""
        if request.method == 'POST':
            test_email = request.POST.get('test_email')
            if test_email:
                try:
                    from django.core.mail import send_mail
                    from django.conf import settings

                    send_mail(
                        subject='Test Email - English Language Center',
                        message='This is a test email to verify your email configuration is working correctly.',
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[test_email],
                        fail_silently=False
                    )

                    messages.success(request, f'Test email sent successfully to {test_email}')
                except Exception as e:
                    messages.error(request, f'Failed to send test email: {str(e)}')
            else:
                messages.error(request, 'Please provide a test email address')

            return redirect('..')

        context = {
            'title': 'Send Test Email',
            'opts': self.model._meta,
        }
        return render(request, 'admin/center/send_test_email.html', context)

    def send_manual_reminders(self, request):
        """Manually trigger payment reminders"""
        if request.method == 'POST':
            try:
                from center.email_services import send_daily_payment_reminders
                reminder_count, overdue_count = send_daily_payment_reminders()

                total_sent = reminder_count + overdue_count
                if total_sent > 0:
                    messages.success(
                        request,
                        f'Successfully sent {reminder_count} payment reminders and {overdue_count} overdue notifications'
                    )
                else:
                    messages.info(request, 'No payment reminders needed to be sent at this time')

            except Exception as e:
                messages.error(request, f'Error sending reminders: {str(e)}')

            return redirect('..')

        # Get preview of what would be sent
        from center.email_services import EmailService
        email_service = EmailService()

        students_to_remind = email_service.get_students_needing_reminders()
        students_overdue = email_service.get_students_with_overdue_payments()

        context = {
            'title': 'Send Manual Payment Reminders',
            'opts': self.model._meta,
            'students_to_remind': students_to_remind,
            'students_overdue': students_overdue,
            'total_emails': len(students_to_remind) + len(students_overdue),
        }
        return render(request, 'admin/center/send_manual_reminders.html', context)

    def days_until_due(self, obj):
        days = obj.days_until_due
        if days < 0:
            return format_html('<span style="color: red;">{} days overdue</span>', abs(days))
        elif days <= 7:
            return format_html('<span style="color: orange;">{} days</span>', days)
        return format_html('{} days', days)
    days_until_due.short_description = 'Days Until Due'


# Register all models with both default admin and custom admin site
admin.site.register(Student, StudentAdmin)
admin.site.register(Teacher, TeacherAdmin)
admin.site.register(Class, ClassAdmin)
admin.site.register(Payment, PaymentAdmin)
admin.site.register(EmailNotification, EmailNotificationAdmin)
admin.site.register(PaymentReminderSettings, PaymentReminderSettingsAdmin)

# Register with custom admin site
admin_site.register(Student, StudentAdmin)
admin_site.register(Teacher, TeacherAdmin)
admin_site.register(Class, ClassAdmin)
admin_site.register(Payment, PaymentAdmin)
admin_site.register(EmailNotification, EmailNotificationAdmin)
admin_site.register(PaymentReminderSettings, PaymentReminderSettingsAdmin)
