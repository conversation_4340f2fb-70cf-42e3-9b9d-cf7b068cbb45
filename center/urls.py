from django.urls import path
from . import views

app_name = 'center'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),
    
    # Student URLs
    path('students/', views.StudentListView.as_view(), name='student_list'),
    path('students/create/', views.StudentCreateView.as_view(), name='student_create'),
    path('students/<int:pk>/', views.StudentDetailView.as_view(), name='student_detail'),
    path('students/<int:pk>/edit/', views.StudentUpdateView.as_view(), name='student_update'),
    path('students/<int:pk>/delete/', views.StudentDeleteView.as_view(), name='student_delete'),
    path('students/<int:pk>/payments/', views.StudentPaymentHistoryView.as_view(), name='student_payment_history'),
    
    # Teacher URLs
    path('teachers/', views.TeacherListView.as_view(), name='teacher_list'),
    path('teachers/create/', views.TeacherCreateView.as_view(), name='teacher_create'),
    path('teachers/<int:pk>/', views.TeacherDetailView.as_view(), name='teacher_detail'),
    path('teachers/<int:pk>/edit/', views.TeacherUpdateView.as_view(), name='teacher_update'),
    path('teachers/<int:pk>/delete/', views.TeacherDeleteView.as_view(), name='teacher_delete'),
    
    # Class URLs
    path('classes/', views.ClassListView.as_view(), name='class_list'),
    path('classes/create/', views.ClassCreateView.as_view(), name='class_create'),
    path('classes/<int:pk>/', views.ClassDetailView.as_view(), name='class_detail'),
    path('classes/<int:pk>/edit/', views.ClassUpdateView.as_view(), name='class_update'),
    path('classes/<int:pk>/delete/', views.ClassDeleteView.as_view(), name='class_delete'),

    # Class Enrollment URLs
    path('classes/<int:class_id>/enroll/', views.enroll_student_in_class, name='enroll_student_in_class'),
    path('classes/<int:class_id>/remove/<int:student_id>/', views.remove_student_from_class, name='remove_student_from_class'),
    path('classes/<int:class_id>/available-students/', views.get_available_students_for_class, name='get_available_students_for_class'),
    
    # Payment URLs
    path('payments/', views.PaymentListView.as_view(), name='payment_list'),
    path('payments/create/', views.PaymentCreateView.as_view(), name='payment_create'),
    path('payments/<int:pk>/', views.PaymentDetailView.as_view(), name='payment_detail'),
    path('payments/<int:pk>/edit/', views.PaymentUpdateView.as_view(), name='payment_update'),
    path('payments/<int:pk>/delete/', views.PaymentDeleteView.as_view(), name='payment_delete'),

    # Settings
    path('settings/', views.settings_view, name='settings'),
    path('settings/edit/', views.SettingsUpdateView.as_view(), name='settings_edit'),
    path('settings/test-email/', views.test_email_settings, name='test_email_settings'),
    path('profile/', views.AdminProfileView.as_view(), name='admin_profile'),
    path('profile/edit/', views.AdminProfileUpdateView.as_view(), name='admin_profile_edit'),

    # Export URLs
    path('export/dashboard/', views.export_dashboard_data, name='export_dashboard'),
    path('export/students/', views.export_students_excel, name='export_students'),
    path('export/teachers/', views.export_teachers_excel, name='export_teachers'),
    path('export/payments/', views.export_payments_excel, name='export_payments'),

    # API endpoints for notifications
    path('send-payment-reminder/', views.send_payment_reminder_api, name='send_payment_reminder_api'),
    path('retry-email-notification/', views.retry_email_notification_api, name='retry_email_notification_api'),
    path('send-all-reminders/', views.send_all_reminders_api, name='send_all_reminders_api'),
]
