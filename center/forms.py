from django import forms
from django.core.exceptions import ValidationError
from .models import Student, Teacher, Class, Payment, PaymentReminderSettings


class StudentForm(forms.ModelForm):
    """Form for creating and updating students"""
    
    class Meta:
        model = Student
        fields = ['full_name', 'email', 'phone_number', 'gender', 'date_of_birth', 'level', 'is_active']
        widgets = {
            'full_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter full name'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Enter email address'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '+1234567890'}),
            'gender': forms.Select(attrs={'class': 'form-control'}),
            'date_of_birth': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'level': forms.Select(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            # Check if email already exists (excluding current instance if updating)
            existing = Student.objects.filter(email=email)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError("A student with this email already exists.")
        return email


class TeacherForm(forms.ModelForm):
    """Form for creating and updating teachers"""
    
    class Meta:
        model = Teacher
        fields = ['name', 'email', 'phone_number', 'subject_specialization', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter full name'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Enter email address'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '+1234567890'}),
            'subject_specialization': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., English Grammar, Conversation'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            existing = Teacher.objects.filter(email=email)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise ValidationError("A teacher with this email already exists.")
        return email


class ClassForm(forms.ModelForm):
    """Form for creating and updating classes"""
    
    class Meta:
        model = Class
        fields = ['class_name', 'level', 'teacher', 'day_of_week', 'start_time', 'end_time', 'capacity', 'is_active']
        widgets = {
            'class_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter class name'}),
            'level': forms.Select(attrs={'class': 'form-control'}),
            'teacher': forms.Select(attrs={'class': 'form-control'}),
            'day_of_week': forms.Select(attrs={'class': 'form-control'}),
            'start_time': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'end_time': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'capacity': forms.NumberInput(attrs={'class': 'form-control', 'min': '1', 'max': '50'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only show active teachers in the dropdown
        self.fields['teacher'].queryset = Teacher.objects.filter(is_active=True)

    def clean(self):
        cleaned_data = super().clean()
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')
        
        if start_time and end_time:
            if start_time >= end_time:
                raise ValidationError("End time must be after start time.")
        
        return cleaned_data


class PaymentForm(forms.ModelForm):
    """Form for creating and updating payments"""
    
    class Meta:
        model = Payment
        fields = ['student', 'amount', 'payment_date', 'payment_method', 'next_due_date', 'is_confirmed', 'notes']
        widgets = {
            'student': forms.Select(attrs={'class': 'form-control'}),
            'amount': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01', 'min': '0'}),
            'payment_date': forms.DateTimeInput(attrs={'class': 'form-control', 'type': 'datetime-local'}),
            'payment_method': forms.Select(attrs={'class': 'form-control'}),
            'next_due_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'is_confirmed': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Optional notes...'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Only show active students in the dropdown
        self.fields['student'].queryset = Student.objects.filter(is_active=True)

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount and amount <= 0:
            raise ValidationError("Amount must be greater than zero.")
        return amount


class StudentSearchForm(forms.Form):
    """Form for searching and filtering students"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by name, email, or phone...'
        })
    )
    level = forms.ChoiceField(
        required=False,
        choices=[('', 'All Levels')] + Student.LEVEL_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    is_active = forms.ChoiceField(
        required=False,
        choices=[('', 'All'), ('true', 'Active'), ('false', 'Inactive')],
        widget=forms.Select(attrs={'class': 'form-control'})
    )


class PaymentSearchForm(forms.Form):
    """Form for searching and filtering payments"""
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by student name...'
        })
    )
    payment_method = forms.ChoiceField(
        required=False,
        choices=[('', 'All Methods')] + Payment.PAYMENT_METHODS,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    is_confirmed = forms.ChoiceField(
        required=False,
        choices=[('', 'All'), ('true', 'Confirmed'), ('false', 'Pending')],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )


class PaymentReminderSettingsForm(forms.ModelForm):
    """Form for editing payment reminder and SMTP settings"""

    class Meta:
        model = PaymentReminderSettings
        fields = [
            'email_enabled', 'from_email', 'reply_to_email',
            'reminder_days_before', 'overdue_reminder_days',
            'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password',
            'smtp_use_tls', 'smtp_use_ssl'
        ]
        widgets = {
            'from_email': forms.EmailInput(attrs={
                'class': 'w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary',
                'placeholder': '<EMAIL>'
            }),
            'reply_to_email': forms.EmailInput(attrs={
                'class': 'w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary',
                'placeholder': '<EMAIL>'
            }),
            'reminder_days_before': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary',
                'placeholder': '7,3,1'
            }),
            'overdue_reminder_days': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary',
                'placeholder': '1,7,14'
            }),
            'smtp_host': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary',
                'placeholder': 'smtp.gmail.com'
            }),
            'smtp_port': forms.NumberInput(attrs={
                'class': 'w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary',
                'placeholder': '587'
            }),
            'smtp_username': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary',
                'placeholder': '<EMAIL>'
            }),
            'smtp_password': forms.PasswordInput(attrs={
                'class': 'w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary',
                'placeholder': 'Your app password'
            }, render_value=True),
            'email_enabled': forms.CheckboxInput(attrs={
                'class': 'rounded focus:ring-2 focus:ring-primary'
            }),
            'smtp_use_tls': forms.CheckboxInput(attrs={
                'class': 'rounded focus:ring-2 focus:ring-primary'
            }),
            'smtp_use_ssl': forms.CheckboxInput(attrs={
                'class': 'rounded focus:ring-2 focus:ring-primary'
            }),
        }

    def clean_reminder_days_before(self):
        value = self.cleaned_data.get('reminder_days_before')
        if value:
            try:
                days = [int(day.strip()) for day in value.split(',') if day.strip()]
                if not days:
                    raise ValidationError("Please enter at least one day.")
                if any(day < 0 for day in days):
                    raise ValidationError("Days must be positive numbers.")
                return ','.join(map(str, days))
            except ValueError:
                raise ValidationError("Please enter comma-separated numbers (e.g., 7,3,1)")
        return value

    def clean_overdue_reminder_days(self):
        value = self.cleaned_data.get('overdue_reminder_days')
        if value:
            try:
                days = [int(day.strip()) for day in value.split(',') if day.strip()]
                if not days:
                    raise ValidationError("Please enter at least one day.")
                if any(day < 0 for day in days):
                    raise ValidationError("Days must be positive numbers.")
                return ','.join(map(str, days))
            except ValueError:
                raise ValidationError("Please enter comma-separated numbers (e.g., 1,7,14)")
        return value

    def clean_smtp_port(self):
        port = self.cleaned_data.get('smtp_port')
        if port and (port < 1 or port > 65535):
            raise ValidationError("Port must be between 1 and 65535.")
        return port

    def clean(self):
        cleaned_data = super().clean()
        smtp_use_tls = cleaned_data.get('smtp_use_tls')
        smtp_use_ssl = cleaned_data.get('smtp_use_ssl')

        if smtp_use_tls and smtp_use_ssl:
            raise ValidationError("Cannot use both TLS and SSL. Please choose one.")

        return cleaned_data
