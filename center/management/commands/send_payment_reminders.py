"""
Management command to send payment reminder emails to students
Usage: python manage.py send_payment_reminders
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from center.email_services import send_daily_payment_reminders
from center.models import PaymentReminderSettings
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Send payment reminder emails to students with upcoming or overdue payments'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what emails would be sent without actually sending them',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force send emails even if notifications are disabled',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        )
    
    def handle(self, *args, **options):
        start_time = timezone.now()
        
        self.stdout.write(
            self.style.SUCCESS(
                f"Starting payment reminder process at {start_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )
        )
        
        # Check if email notifications are enabled
        settings = PaymentReminderSettings.get_settings()
        
        if not settings.email_enabled and not options['force']:
            self.stdout.write(
                self.style.WARNING(
                    "Email notifications are disabled. Use --force to send anyway."
                )
            )
            return
        
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No emails will be sent")
            )
            self._dry_run(options['verbose'])
        else:
            self._send_reminders(options['verbose'], options['force'])
        
        end_time = timezone.now()
        duration = end_time - start_time
        
        self.stdout.write(
            self.style.SUCCESS(
                f"Payment reminder process completed in {duration.total_seconds():.2f} seconds"
            )
        )
    
    def _dry_run(self, verbose):
        """Show what emails would be sent without sending them"""
        from center.email_services import EmailService
        
        email_service = EmailService()
        
        # Get students needing reminders
        students_to_remind = email_service.get_students_needing_reminders()
        students_overdue = email_service.get_students_with_overdue_payments()
        
        self.stdout.write(f"\n📧 PAYMENT REMINDERS TO SEND: {len(students_to_remind)}")
        for item in students_to_remind:
            student = item['student']
            payment = item['payment']
            days = item['days_until_due']
            
            self.stdout.write(
                f"  • {student.full_name} ({student.email}) - ${payment.amount} due in {days} day(s)"
            )
            
            if verbose:
                self.stdout.write(f"    Payment due: {payment.next_due_date}")
        
        self.stdout.write(f"\n🚨 OVERDUE NOTIFICATIONS TO SEND: {len(students_overdue)}")
        for item in students_overdue:
            student = item['student']
            payment = item['payment']
            days = item['days_overdue']
            
            self.stdout.write(
                f"  • {student.full_name} ({student.email}) - ${payment.amount} overdue by {days} day(s)"
            )
            
            if verbose:
                self.stdout.write(f"    Payment was due: {payment.next_due_date}")
        
        total_emails = len(students_to_remind) + len(students_overdue)
        self.stdout.write(f"\n📊 TOTAL EMAILS TO SEND: {total_emails}")
    
    def _send_reminders(self, verbose, force):
        """Actually send the reminder emails"""
        try:
            reminder_count, overdue_count = send_daily_payment_reminders()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"✅ Successfully sent {reminder_count} payment reminders"
                )
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"✅ Successfully sent {overdue_count} overdue notifications"
                )
            )
            
            total_sent = reminder_count + overdue_count
            
            if total_sent == 0:
                self.stdout.write(
                    self.style.WARNING("ℹ️  No emails needed to be sent today")
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"📧 Total emails sent: {total_sent}"
                    )
                )
            
            if verbose:
                self._show_email_statistics()
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"❌ Error sending reminders: {str(e)}")
            )
            logger.error(f"Error in send_payment_reminders command: {str(e)}")
    
    def _show_email_statistics(self):
        """Show email statistics"""
        from center.models import EmailNotification
        from datetime import date
        
        today = date.today()
        
        # Today's email stats
        today_emails = EmailNotification.objects.filter(
            scheduled_date__date=today
        )
        
        sent_today = today_emails.filter(status='SENT').count()
        failed_today = today_emails.filter(status='FAILED').count()
        pending_today = today_emails.filter(status='PENDING').count()
        
        self.stdout.write(f"\n📊 TODAY'S EMAIL STATISTICS:")
        self.stdout.write(f"  ✅ Sent: {sent_today}")
        self.stdout.write(f"  ❌ Failed: {failed_today}")
        self.stdout.write(f"  ⏳ Pending: {pending_today}")
        
        # This week's stats
        from datetime import timedelta
        week_start = today - timedelta(days=today.weekday())
        
        week_emails = EmailNotification.objects.filter(
            scheduled_date__date__gte=week_start,
            scheduled_date__date__lte=today
        )
        
        sent_week = week_emails.filter(status='SENT').count()
        failed_week = week_emails.filter(status='FAILED').count()
        
        self.stdout.write(f"\n📊 THIS WEEK'S EMAIL STATISTICS:")
        self.stdout.write(f"  ✅ Sent: {sent_week}")
        self.stdout.write(f"  ❌ Failed: {failed_week}")
        
        # Show recent failures if any
        recent_failures = EmailNotification.objects.filter(
            status='FAILED',
            scheduled_date__date=today
        ).select_related('student')[:5]
        
        if recent_failures:
            self.stdout.write(f"\n⚠️  RECENT FAILURES:")
            for notification in recent_failures:
                self.stdout.write(
                    f"  • {notification.student.full_name} ({notification.student.email}): {notification.error_message[:100]}"
                )
