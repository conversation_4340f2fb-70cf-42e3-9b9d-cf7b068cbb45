from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import date, timedelta
from center.models import Student, Teacher, Class, Payment
import random


class Command(BaseCommand):
    help = 'Create sample data for the language center'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create Teachers
        teachers_data = [
            {'name': '<PERSON>', 'email': '<EMAIL>', 'phone_number': '+1234567890', 'subject_specialization': 'Grammar & Writing'},
            {'name': '<PERSON>', 'email': '<EMAIL>', 'phone_number': '+1234567891', 'subject_specialization': 'Conversation & Speaking'},
            {'name': '<PERSON>', 'email': '<EMAIL>', 'phone_number': '+1234567892', 'subject_specialization': 'Business English'},
            {'name': '<PERSON>', 'email': '<EMAIL>', 'phone_number': '+1234567893', 'subject_specialization': 'IELTS Preparation'},
            {'name': '<PERSON>', 'email': '<EMAIL>', 'phone_number': '+1234567894', 'subject_specialization': 'General English'},
        ]
        
        teachers = []
        for teacher_data in teachers_data:
            teacher, created = Teacher.objects.get_or_create(
                email=teacher_data['email'],
                defaults=teacher_data
            )
            teachers.append(teacher)
            if created:
                self.stdout.write(f'Created teacher: {teacher.name}')
        
        # Create Students
        students_data = [
            {'full_name': 'Alice Cooper', 'email': '<EMAIL>', 'phone_number': '+1555123456', 'gender': 'F', 'level': 'BEGINNER'},
            {'full_name': 'Bob Smith', 'email': '<EMAIL>', 'phone_number': '+1555123457', 'gender': 'M', 'level': 'INTERMEDIATE'},
            {'full_name': 'Carol Davis', 'email': '<EMAIL>', 'phone_number': '+1555123458', 'gender': 'F', 'level': 'ADVANCED'},
            {'full_name': 'Daniel Brown', 'email': '<EMAIL>', 'phone_number': '+1555123459', 'gender': 'M', 'level': 'UPPER_INTERMEDIATE'},
            {'full_name': 'Eva Martinez', 'email': '<EMAIL>', 'phone_number': '+1555123460', 'gender': 'F', 'level': 'ELEMENTARY'},
            {'full_name': 'Frank Wilson', 'email': '<EMAIL>', 'phone_number': '+1555123461', 'gender': 'M', 'level': 'BEGINNER'},
            {'full_name': 'Grace Lee', 'email': '<EMAIL>', 'phone_number': '+1555123462', 'gender': 'F', 'level': 'INTERMEDIATE'},
            {'full_name': 'Henry Taylor', 'email': '<EMAIL>', 'phone_number': '+1555123463', 'gender': 'M', 'level': 'ADVANCED'},
            {'full_name': 'Iris Johnson', 'email': '<EMAIL>', 'phone_number': '+1555123464', 'gender': 'F', 'level': 'UPPER_INTERMEDIATE'},
            {'full_name': 'Jack Anderson', 'email': '<EMAIL>', 'phone_number': '+1555123465', 'gender': 'M', 'level': 'ELEMENTARY'},
        ]
        
        students = []
        for i, student_data in enumerate(students_data):
            # Random birth date between 18-50 years ago
            birth_year = date.today().year - random.randint(18, 50)
            student_data['date_of_birth'] = date(birth_year, random.randint(1, 12), random.randint(1, 28))
            
            student, created = Student.objects.get_or_create(
                email=student_data['email'],
                defaults=student_data
            )
            students.append(student)
            if created:
                self.stdout.write(f'Created student: {student.full_name}')
        
        # Create Classes
        classes_data = [
            {'class_name': 'Beginner Morning', 'level': 'BEGINNER', 'day_of_week': 'MONDAY', 'start_time': '09:00', 'end_time': '10:30', 'capacity': 15},
            {'class_name': 'Intermediate Evening', 'level': 'INTERMEDIATE', 'day_of_week': 'TUESDAY', 'start_time': '18:00', 'end_time': '19:30', 'capacity': 12},
            {'class_name': 'Advanced Conversation', 'level': 'ADVANCED', 'day_of_week': 'WEDNESDAY', 'start_time': '14:00', 'end_time': '15:30', 'capacity': 10},
            {'class_name': 'Business English', 'level': 'UPPER_INTERMEDIATE', 'day_of_week': 'THURSDAY', 'start_time': '17:00', 'end_time': '18:30', 'capacity': 8},
            {'class_name': 'Elementary Weekend', 'level': 'ELEMENTARY', 'day_of_week': 'SATURDAY', 'start_time': '10:00', 'end_time': '11:30', 'capacity': 15},
        ]
        
        classes = []
        for i, class_data in enumerate(classes_data):
            class_data['teacher'] = teachers[i % len(teachers)]
            
            class_obj, created = Class.objects.get_or_create(
                class_name=class_data['class_name'],
                defaults=class_data
            )
            classes.append(class_obj)
            if created:
                self.stdout.write(f'Created class: {class_obj.class_name}')
                
                # Assign students to classes based on level
                matching_students = [s for s in students if s.level == class_obj.level]
                if matching_students:
                    # Assign 3-5 students per class
                    selected_students = random.sample(matching_students, min(len(matching_students), random.randint(3, 5)))
                    class_obj.students.set(selected_students)
        
        # Create Payments
        for student in students:
            # Create 1-3 payments per student
            num_payments = random.randint(1, 3)
            for _ in range(num_payments):
                payment_date = timezone.now() - timedelta(days=random.randint(1, 90))
                next_due_date = payment_date.date() + timedelta(days=30)
                
                Payment.objects.create(
                    student=student,
                    amount=random.choice([150.00, 200.00, 250.00, 300.00]),
                    payment_date=payment_date,
                    payment_method=random.choice(['CASH', 'CARD', 'BANK_TRANSFER']),
                    next_due_date=next_due_date,
                    is_confirmed=random.choice([True, True, True, False])  # 75% confirmed
                )
        
        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))
        self.stdout.write(f'Created {Teacher.objects.count()} teachers')
        self.stdout.write(f'Created {Student.objects.count()} students')
        self.stdout.write(f'Created {Class.objects.count()} classes')
        self.stdout.write(f'Created {Payment.objects.count()} payments')
