# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: contrib/sites/apps.py:11
msgid "Sites"
msgstr ""

#: contrib/sites/models.py:30
msgid "The domain name cannot contain any spaces or tabs."
msgstr ""

#: contrib/sites/models.py:81
msgid "domain name"
msgstr ""

#: contrib/sites/models.py:83
msgid "display name"
msgstr ""

#: contrib/sites/models.py:88
msgid "site"
msgstr ""

#: contrib/sites/models.py:89
msgid "sites"
msgstr ""
