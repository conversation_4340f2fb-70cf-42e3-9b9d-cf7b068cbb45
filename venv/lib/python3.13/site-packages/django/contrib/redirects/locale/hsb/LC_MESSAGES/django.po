# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2016,2019,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-02-01 22:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Upper Sorbian (http://www.transifex.com/django/django/"
"language/hsb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hsb\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"

msgid "Redirects"
msgstr "Dalesposrědkowanja"

msgid "site"
msgstr "sydło"

msgid "redirect from"
msgstr "dalesposrědkowanje wot"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"To měło absolutna šćežka być, nimo domenoweho mjena. Přikład: \"/events/"
"search/\"."

msgid "redirect to"
msgstr "dalesposrědkowanje do"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"To móže pak absolutna šćežka być (kaž horjeka) pak dospołny URL, kotryž so z "
"\"https:/\" započina."

msgid "redirect"
msgstr "dalesposrědkowanje"

msgid "redirects"
msgstr "dalesposrědkowanja"
