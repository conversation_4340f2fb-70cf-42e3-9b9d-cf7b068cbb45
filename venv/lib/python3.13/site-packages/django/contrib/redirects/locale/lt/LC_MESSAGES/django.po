# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: Matas Dailyda <<EMAIL>>\n"
"Language-Team: Lithuanian (http://www.transifex.com/django/django/language/"
"lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < "
"11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? "
"1 : n % 1 != 0 ? 2: 3);\n"

msgid "Redirects"
msgstr "Nukreipimai"

msgid "site"
msgstr "tinklalapis"

msgid "redirect from"
msgstr "nukreiptas (redirect) iš"

msgid ""
"This should be an absolute path, excluding the domain name. Example: '/"
"events/search/'."
msgstr ""
"Turi būti absoliutus adresas neįtraukiant domaino. Pavyzdžiui: '/events/"
"search/'."

msgid "redirect to"
msgstr "nukreipti(redirect) į"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with "
"'http://'."
msgstr ""
"Gali būti absoliutus adresas (kaip viršuj) arba pilnas URL pradedant "
"'http://'."

msgid "redirect"
msgstr "nukreipti"

msgid "redirects"
msgstr "nukreipia"
