# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2014-2015
# <AUTHOR> <EMAIL>, 2011
# <AUTHOR> <EMAIL>, 2022
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2022-04-24 18:32+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Esperanto (http://www.transifex.com/django/django/language/"
"eo/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: eo\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Alidirektigiloj"

msgid "site"
msgstr "retejo"

msgid "redirect from"
msgstr "alidirektigo de"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""

msgid "redirect to"
msgstr "alidirektigo al"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""

msgid "redirect"
msgstr "alidirektigilo"

msgid "redirects"
msgstr "alidirektigiloj"
