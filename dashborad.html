<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Language Center - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1d4ed8',
                        accent: '#10b981',
                        dark: '#1e293b',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar {
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .active-tab {
            border-bottom: 3px solid #3b82f6;
            color: #3b82f6;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .progress-bar {
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            transition: width 0.5s ease-in-out;
        }
        .notification-dot {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Main Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar bg-dark text-white w-64 space-y-6 py-7 px-2 fixed inset-y-0 left-0 transform -translate-x-full md:translate-x-0 transition duration-200 ease-in-out z-10">
            <div class="flex items-center space-x-2 px-4">
                <i class="fas fa-book-open text-accent text-2xl"></i>
                <h1 class="text-xl font-bold">English Center</h1>
            </div>
            
            <nav>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 bg-secondary rounded-lg">
                    <i class="fas fa-chart-line"></i>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg mt-2">
                    <i class="fas fa-user-graduate"></i>
                    <span>Students</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <span>Teachers</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-users"></i>
                    <span>Classes</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-receipt"></i>
                    <span>Payments</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </nav>
            
            <div class="absolute bottom-0 w-full left-0 p-4 border-t border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="relative">
                        <div class="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10"></div>
                        <div class="notification-dot"></div>
                    </div>
                    <div>
                        <p class="font-medium">Admin User</p>
                        <p class="text-sm text-gray-400"><EMAIL></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 md:ml-64">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm">
                <div class="flex justify-between items-center p-4">
                    <div class="flex items-center">
                        <button class="md:hidden text-gray-500 focus:outline-none">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 class="text-xl font-semibold ml-2">Dashboard Overview</h2>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="Search..." class="bg-gray-100 rounded-full py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-primary">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <button class="relative text-gray-500">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                        </button>
                        <div class="flex items-center space-x-2">
                            <div class="bg-gray-200 border-2 border-dashed rounded-xl w-8 h-8"></div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Dashboard Content -->
            <main class="p-4 md:p-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <!-- Students Card -->
                    <div class="stat-card bg-white rounded-xl shadow-md p-6 transition duration-300">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-gray-500">Total Students</p>
                                <h3 class="text-3xl font-bold mt-2">248</h3>
                                <div class="flex items-center mt-2">
                                    <span class="text-green-500 mr-1"><i class="fas fa-arrow-up"></i> 12%</span>
                                    <span class="text-gray-500 text-sm">from last month</span>
                                </div>
                            </div>
                            <div class="bg-blue-100 p-3 rounded-lg">
                                <i class="fas fa-user-graduate text-blue-500 text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex justify-between text-sm text-gray-500 mb-1">
                                <span>Active</span>
                                <span>198</span>
                            </div>
                            <div class="progress-bar bg-gray-200">
                                <div class="progress-fill bg-blue-500" style="width: 80%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Teachers Card -->
                    <div class="stat-card bg-white rounded-xl shadow-md p-6 transition duration-300">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-gray-500">Teachers</p>
                                <h3 class="text-3xl font-bold mt-2">18</h3>
                                <div class="flex items-center mt-2">
                                    <span class="text-green-500 mr-1"><i class="fas fa-arrow-up"></i> 5%</span>
                                    <span class="text-gray-500 text-sm">from last month</span>
                                </div>
                            </div>
                            <div class="bg-green-100 p-3 rounded-lg">
                                <i class="fas fa-chalkboard-teacher text-green-500 text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex justify-between text-sm text-gray-500 mb-1">
                                <span>Active</span>
                                <span>15</span>
                            </div>
                            <div class="progress-bar bg-gray-200">
                                <div class="progress-fill bg-green-500" style="width: 83%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Classes Card -->
                    <div class="stat-card bg-white rounded-xl shadow-md p-6 transition duration-300">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-gray-500">Active Classes</p>
                                <h3 class="text-3xl font-bold mt-2">24</h3>
                                <div class="flex items-center mt-2">
                                    <span class="text-green-500 mr-1"><i class="fas fa-arrow-up"></i> 8%</span>
                                    <span class="text-gray-500 text-sm">from last month</span>
                                </div>
                            </div>
                            <div class="bg-purple-100 p-3 rounded-lg">
                                <i class="fas fa-users text-purple-500 text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex justify-between text-sm text-gray-500 mb-1">
                                <span>Capacity</span>
                                <span>82%</span>
                            </div>
                            <div class="progress-bar bg-gray-200">
                                <div class="progress-fill bg-purple-500" style="width: 82%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Revenue Card -->
                    <div class="stat-card bg-white rounded-xl shadow-md p-6 transition duration-300">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-gray-500">Monthly Revenue</p>
                                <h3 class="text-3xl font-bold mt-2">$12,450</h3>
                                <div class="flex items-center mt-2">
                                    <span class="text-green-500 mr-1"><i class="fas fa-arrow-up"></i> 15%</span>
                                    <span class="text-gray-500 text-sm">from last month</span>
                                </div>
                            </div>
                            <div class="bg-yellow-100 p-3 rounded-lg">
                                <i class="fas fa-dollar-sign text-yellow-500 text-2xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex justify-between text-sm text-gray-500 mb-1">
                                <span>Target</span>
                                <span>$15,000</span>
                            </div>
                            <div class="progress-bar bg-gray-200">
                                <div class="progress-fill bg-yellow-500" style="width: 83%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Student Distribution Chart -->
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-bold">Student Distribution by Level</h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 bg-gray-100 rounded-lg text-sm">Monthly</button>
                                <button class="px-3 py-1 bg-primary text-white rounded-lg text-sm">Yearly</button>
                            </div>
                        </div>
                        <div class="h-64">
                            <canvas id="studentLevelChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- Revenue Chart -->
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-bold">Revenue Overview</h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 bg-gray-100 rounded-lg text-sm">Monthly</button>
                                <button class="px-3 py-1 bg-primary text-white rounded-lg text-sm">Yearly</button>
                            </div>
                        </div>
                        <div class="h-64">
                            <canvas id="revenueChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity Section -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <!-- Upcoming Classes -->
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-bold">Upcoming Classes</h3>
                            <a href="#" class="text-primary text-sm">View All</a>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- Class 1 -->
                            <div class="flex items-start border-b pb-4">
                                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-users text-blue-500"></i>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-medium">Intermediate Conversation</h4>
                                    <p class="text-sm text-gray-500">Mon, Wed, Fri • 10:00 AM - 11:30 AM</p>
                                    <div class="flex items-center mt-1">
                                        <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">Room 201</span>
                                        <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">Sarah Johnson</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Class 2 -->
                            <div class="flex items-start border-b pb-4">
                                <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-users text-purple-500"></i>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-medium">Advanced Grammar</h4>
                                    <p class="text-sm text-gray-500">Tue, Thu • 2:00 PM - 3:30 PM</p>
                                    <div class="flex items-center mt-1">
                                        <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">Room 105</span>
                                        <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">Michael Chen</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Class 3 -->
                            <div class="flex items-start">
                                <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-users text-yellow-500"></i>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-medium">Beginner Writing</h4>
                                    <p class="text-sm text-gray-500">Sat • 9:00 AM - 12:00 PM</p>
                                    <div class="flex items-center mt-1">
                                        <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">Room 302</span>
                                        <span class="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">Emma Rodriguez</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Payments -->
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg font-bold">Recent Payments</h3>
                            <a href="#" class="text-primary text-sm">View All</a>
                        </div>
                        
                        <div class="space-y-4">
                            <!-- Payment 1 -->
                            <div class="flex items-center justify-between border-b pb-4">
                                <div class="flex items-center">
                                    <div class="bg-green-100 p-2 rounded-lg mr-3">
                                        <i class="fas fa-check-circle text-green-500"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Sarah Johnson</h4>
                                        <p class="text-sm text-gray-500">Intermediate Conversation</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-green-500">$150.00</p>
                                    <p class="text-sm text-gray-500">Today, 10:30 AM</p>
                                </div>
                            </div>
                            
                            <!-- Payment 2 -->
                            <div class="flex items-center justify-between border-b pb-4">
                                <div class="flex items-center">
                                    <div class="bg-green-100 p-2 rounded-lg mr-3">
                                        <i class="fas fa-check-circle text-green-500"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">David Wilson</h4>
                                        <p class="text-sm text-gray-500">Advanced Listening</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-green-500">$180.00</p>
                                    <p class="text-sm text-gray-500">Yesterday, 3:15 PM</p>
                                </div>
                            </div>
                            
                            <!-- Payment 3 -->
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="bg-green-100 p-2 rounded-lg mr-3">
                                        <i class="fas fa-check-circle text-green-500"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium">Emma Rodriguez</h4>
                                        <p class="text-sm text-gray-500">Beginner Writing</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-green-500">$120.00</p>
                                    <p class="text-sm text-gray-500">Dec 12, 2:45 PM</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <div class="mb-6">
                            <h3 class="text-lg font-bold">Quick Actions</h3>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <a href="#" class="bg-blue-50 hover:bg-blue-100 rounded-lg p-4 text-center transition duration-300">
                                <div class="bg-blue-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-user-plus text-blue-500"></i>
                                </div>
                                <span class="font-medium">Add Student</span>
                            </a>
                            
                            <a href="#" class="bg-green-50 hover:bg-green-100 rounded-lg p-4 text-center transition duration-300">
                                <div class="bg-green-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-chalkboard-teacher text-green-500"></i>
                                </div>
                                <span class="font-medium">Add Teacher</span>
                            </a>
                            
                            <a href="#" class="bg-purple-50 hover:bg-purple-100 rounded-lg p-4 text-center transition duration-300">
                                <div class="bg-purple-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-users text-purple-500"></i>
                                </div>
                                <span class="font-medium">Create Class</span>
                            </a>
                            
                            <a href="#" class="bg-yellow-50 hover:bg-yellow-100 rounded-lg p-4 text-center transition duration-300">
                                <div class="bg-yellow-100 p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-dollar-sign text-yellow-500"></i>
                                </div>
                                <span class="font-medium">Record Payment</span>
                            </a>
                        </div>
                        
                        <div class="mt-8">
                            <h3 class="text-lg font-bold mb-4">System Status</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Database</span>
                                    <span class="font-medium text-green-500">Operational</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Server Uptime</span>
                                    <span class="font-medium">99.8%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Storage</span>
                                    <span class="font-medium">42% used</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Last Backup</span>
                                    <span class="font-medium">Dec 15, 2023</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Student Registration -->
                <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-bold">Recent Student Registrations</h3>
                        <a href="#" class="text-primary text-sm">View All Students</a>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registration Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned Classes</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="bg-gray-200 border-2 border-dashed rounded-xl w-8 h-8 mr-3"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">Sophia Martinez</div>
                                                <div class="text-sm text-gray-500"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Intermediate</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        Dec 15, 2023
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        2 classes
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-primary hover:text-secondary">View Profile</a>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="bg-gray-200 border-2 border-dashed rounded-xl w-8 h-8 mr-3"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">James Wilson</div>
                                                <div class="text-sm text-gray-500"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Beginner</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Pending</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        Dec 14, 2023
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        1 class
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-primary hover:text-secondary">View Profile</a>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="bg-gray-200 border-2 border-dashed rounded-xl w-8 h-8 mr-3"></div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">Olivia Kim</div>
                                                <div class="text-sm text-gray-500"><EMAIL></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Advanced</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        Dec 12, 2023
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        3 classes
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="#" class="text-primary hover:text-secondary">View Profile</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
            
            <!-- Footer -->
            <footer class="bg-white border-t p-4 mt-8">
                <div class="text-center text-gray-500 text-sm">
                    <p>© 2023 English Language Center Management System. All rights reserved.</p>
                </div>
            </footer>
        </div>
    </div>

    <script>
        // Mobile sidebar toggle
        document.querySelector('.md\\:hidden').addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('-translate-x-full');
        });
        
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            // Student Level Chart
            const studentLevelCtx = document.getElementById('studentLevelChart').getContext('2d');
            const studentLevelChart = new Chart(studentLevelCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Beginner', 'Intermediate', 'Upper-Intermediate', 'Advanced'],
                    datasets: [{
                        data: [65, 85, 45, 53],
                        backgroundColor: [
                            '#fbbf24',
                            '#3b82f6',
                            '#8b5cf6',
                            '#10b981'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 12,
                                padding: 20
                            }
                        }
                    }
                }
            });
            
            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            const revenueChart = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                    datasets: [{
                        label: 'Revenue ($)',
                        data: [8500, 9200, 10200, 11000, 11800, 12450],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        borderWidth: 3,
                        pointBackgroundColor: '#10b981',
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        fill: true,
                        tension: 0.3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                drawBorder: false
                            },
                            ticks: {
                                callback: function(value) {
                                    return '$' + value;
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>