<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Language Center - Students</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1d4ed8',
                        accent: '#10b981',
                        dark: '#1e293b',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar {
            transition: all 0.3s ease;
        }
        .student-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .active-tab {
            border-bottom: 3px solid #3b82f6;
            color: #3b82f6;
        }
        .modal {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Main Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar bg-dark text-white w-64 space-y-6 py-7 px-2 fixed inset-y-0 left-0 transform -translate-x-full md:translate-x-0 transition duration-200 ease-in-out z-10">
            <div class="flex items-center space-x-2 px-4">
                <i class="fas fa-book-open text-accent text-2xl"></i>
                <h1 class="text-xl font-bold">English Center</h1>
            </div>
            
            <nav>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-chart-line"></i>
                    <span>Dashboard</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 bg-secondary rounded-lg mt-2">
                    <i class="fas fa-user-graduate"></i>
                    <span>Students</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <span>Teachers</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-users"></i>
                    <span>Classes</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-receipt"></i>
                    <span>Payments</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </nav>
            
            <div class="absolute bottom-0 w-full left-0 p-4 border-t border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10"></div>
                    <div>
                        <p class="font-medium">Admin User</p>
                        <p class="text-sm text-gray-400"><EMAIL></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 md:ml-64">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm">
                <div class="flex justify-between items-center p-4">
                    <div class="flex items-center">
                        <button class="md:hidden text-gray-500 focus:outline-none">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 class="text-xl font-semibold ml-2">Students Management</h2>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" placeholder="Search students..." class="bg-gray-100 rounded-full py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-primary">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        <button class="relative text-gray-500">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                        </button>
                        <div class="flex items-center space-x-2">
                            <div class="bg-gray-200 border-2 border-dashed rounded-xl w-8 h-8"></div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Students Content -->
            <main class="p-4 md:p-6">
                <!-- Student Filters and Actions -->
                <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <div class="flex space-x-2 mb-4 md:mb-0">
                            <button class="px-4 py-2 bg-primary text-white rounded-lg flex items-center" onclick="openAddStudentModal()">
                                <i class="fas fa-plus mr-2"></i> Add Student
                            </button>
                            <button class="px-4 py-2 border border-gray-300 rounded-lg flex items-center">
                                <i class="fas fa-filter mr-2"></i> Filter
                            </button>
                            <button class="px-4 py-2 border border-gray-300 rounded-lg flex items-center">
                                <i class="fas fa-download mr-2"></i> Export
                            </button>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <select class="bg-gray-100 border-0 rounded-lg py-2 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option>All Levels</option>
                                    <option>Beginner</option>
                                    <option>Intermediate</option>
                                    <option>Upper-Intermediate</option>
                                    <option>Advanced</option>
                                </select>
                            </div>
                            
                            <div class="relative">
                                <select class="bg-gray-100 border-0 rounded-lg py-2 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option>All Status</option>
                                    <option>Active</option>
                                    <option>Pending</option>
                                    <option>Suspended</option>
                                    <option>Completed</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Student Tabs -->
                <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8">
                            <button class="py-4 px-1 border-b-2 font-medium text-sm active-tab">
                                All Students (248)
                            </button>
                            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Active (198)
                            </button>
                            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Pending (32)
                            </button>
                            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Suspended (12)
                            </button>
                            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Completed (6)
                            </button>
                        </nav>
                    </div>
                </div>
                
                <!-- Student Cards Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-6">
                    <!-- Student Card 1 -->
                    <div class="student-card bg-white rounded-xl shadow-md overflow-hidden transition duration-300">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-12 h-12 mr-3"></div>
                                    <div>
                                        <h3 class="font-bold">Sarah Johnson</h3>
                                        <p class="text-sm text-gray-500"><EMAIL></p>
                                    </div>
                                </div>
                                <div class="dropdown relative">
                                    <button class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="toggleDropdown(this)">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Profile</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Payment History</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Level:</span>
                                    <span class="font-medium">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Intermediate</span>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Phone:</span>
                                    <span class="font-medium">+****************</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Status:</span>
                                    <span class="font-medium">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Joined:</span>
                                    <span class="font-medium">15 Jan 2023</span>
                                </div>
                            </div>
                            
                            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                                <button class="px-3 py-1 bg-blue-50 text-blue-600 rounded-lg text-sm flex items-center">
                                    <i class="fas fa-envelope mr-1"></i> Message
                                </button>
                                <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-sm flex items-center">
                                    <i class="fas fa-eye mr-1"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Student Card 2 -->
                    <div class="student-card bg-white rounded-xl shadow-md overflow-hidden transition duration-300">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-12 h-12 mr-3"></div>
                                    <div>
                                        <h3 class="font-bold">Michael Chen</h3>
                                        <p class="text-sm text-gray-500"><EMAIL></p>
                                    </div>
                                </div>
                                <div class="dropdown relative">
                                    <button class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="toggleDropdown(this)">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Profile</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Payment History</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Level:</span>
                                    <span class="font-medium">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Advanced</span>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Phone:</span>
                                    <span class="font-medium">+****************</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Status:</span>
                                    <span class="font-medium">
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Pending</span>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Joined:</span>
                                    <span class="font-medium">22 Feb 2023</span>
                                </div>
                            </div>
                            
                            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                                <button class="px-3 py-1 bg-blue-50 text-blue-600 rounded-lg text-sm flex items-center">
                                    <i class="fas fa-envelope mr-1"></i> Message
                                </button>
                                <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-sm flex items-center">
                                    <i class="fas fa-eye mr-1"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Student Card 3 -->
                    <div class="student-card bg-white rounded-xl shadow-md overflow-hidden transition duration-300">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-12 h-12 mr-3"></div>
                                    <div>
                                        <h3 class="font-bold">Emma Rodriguez</h3>
                                        <p class="text-sm text-gray-500"><EMAIL></p>
                                    </div>
                                </div>
                                <div class="dropdown relative">
                                    <button class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="toggleDropdown(this)">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Profile</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Payment History</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Level:</span>
                                    <span class="font-medium">
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Beginner</span>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Phone:</span>
                                    <span class="font-medium">+****************</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Status:</span>
                                    <span class="font-medium">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Joined:</span>
                                    <span class="font-medium">05 Mar 2023</span>
                                </div>
                            </div>
                            
                            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                                <button class="px-3 py-1 bg-blue-50 text-blue-600 rounded-lg text-sm flex items-center">
                                    <i class="fas fa-envelope mr-1"></i> Message
                                </button>
                                <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-sm flex items-center">
                                    <i class="fas fa-eye mr-1"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Student Card 4 -->
                    <div class="student-card bg-white rounded-xl shadow-md overflow-hidden transition duration-300">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-12 h-12 mr-3"></div>
                                    <div>
                                        <h3 class="font-bold">David Wilson</h3>
                                        <p class="text-sm text-gray-500"><EMAIL></p>
                                    </div>
                                </div>
                                <div class="dropdown relative">
                                    <button class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="toggleDropdown(this)">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Profile</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Payment History</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Level:</span>
                                    <span class="font-medium">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Intermediate</span>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Phone:</span>
                                    <span class="font-medium">+****************</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Status:</span>
                                    <span class="font-medium">
                                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Joined:</span>
                                    <span class="font-medium">18 Apr 2023</span>
                                </div>
                            </div>
                            
                            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                                <button class="px-3 py-1 bg-blue-50 text-blue-600 rounded-lg text-sm flex items-center">
                                    <i class="fas fa-envelope mr-1"></i> Message
                                </button>
                                <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-sm flex items-center">
                                    <i class="fas fa-eye mr-1"></i> View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <div class="bg-white rounded-xl shadow-md p-6 flex justify-between items-center">
                    <p class="text-gray-500">Showing 4 of 248 students</p>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 border border-gray-300 rounded-lg flex items-center">
                            <i class="fas fa-chevron-left mr-2"></i> Previous
                        </button>
                        <button class="px-4 py-2 bg-primary text-white rounded-lg">1</button>
                        <button class="px-4 py-2 border border-gray-300 rounded-lg">2</button>
                        <button class="px-4 py-2 border border-gray-300 rounded-lg">3</button>
                        <button class="px-4 py-2 border border-gray-300 rounded-lg flex items-center">
                            Next <i class="fas fa-chevron-right ml-2"></i>
                        </button>
                    </div>
                </div>
            </main>
            
            <!-- Footer -->
            <footer class="bg-white border-t p-4 mt-8">
                <div class="text-center text-gray-500 text-sm">
                    <p>© 2023 English Language Center Management System. All rights reserved.</p>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Add Student Modal -->
    <div id="addStudentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="modal bg-white rounded-xl shadow-xl w-full max-w-2xl max-h-screen overflow-y-auto fade-in">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold">Add New Student</h3>
                    <button class="text-gray-400 hover:text-gray-600" onclick="closeAddStudentModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label class="block text-gray-700 mb-2">First Name</label>
                            <input type="text" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">Last Name</label>
                            <input type="text" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">Email</label>
                            <input type="email" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">Phone</label>
                            <input type="tel" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">Date of Birth</label>
                            <input type="date" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">Gender</label>
                            <select class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                <option>Male</option>
                                <option>Female</option>
                                <option>Other</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">Level</label>
                            <select class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                <option>Beginner</option>
                                <option>Intermediate</option>
                                <option>Upper-Intermediate</option>
                                <option>Advanced</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">Status</label>
                            <select class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                <option>Active</option>
                                <option>Pending</option>
                                <option>Suspended</option>
                                <option>Completed</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-gray-700 mb-2">Address</label>
                        <textarea class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-gray-700 mb-2">Notes</label>
                        <textarea class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" rows="3"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-4">
                        <button type="button" class="px-6 py-2 border border-gray-300 rounded-lg" onclick="closeAddStudentModal()">
                            Cancel
                        </button>
                        <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg">
                            Save Student
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        // Mobile sidebar toggle
        document.querySelector('.md\\:hidden').addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('-translate-x-full');
        });
        
        // Toggle dropdown menu
        function toggleDropdown(button) {
            const dropdown = button.nextElementSibling;
            dropdown.classList.toggle('hidden');
            
            // Close other dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdown) {
                    menu.classList.add('hidden');
                }
            });
        }
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.matches('.dropdown button')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.add('hidden');
                });
            }
        });
        
        // Modal functions
        function openAddStudentModal() {
            document.getElementById('addStudentModal').classList.remove('hidden');
        }
        
        function closeAddStudentModal() {
            document.getElementById('addStudentModal').classList.add('hidden');
        }
        
        // Close modal when clicking outside
        document.getElementById('addStudentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAddStudentModal();
            }
        });
    </script>
</body>
</html>