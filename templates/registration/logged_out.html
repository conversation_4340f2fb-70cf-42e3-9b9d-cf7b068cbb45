<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Language Center - Logout</title>
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1d4ed8',
                        accent: '#10b981',
                        dark: '#1e293b',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        .logout-card {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .language-selector:hover .language-dropdown {
            display: block;
        }
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #f00;
            border-radius: 50%;
            animation: fall linear forwards;
        }
        @keyframes fall {
            to {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <!-- Confetti Animation Container -->
    <div id="confetti-container" class="fixed inset-0 pointer-events-none z-0 overflow-hidden"></div>
    
    <!-- Language Selector -->
    <div class="absolute top-4 right-4 z-10 language-selector">
        <button class="flex items-center text-gray-600 hover:text-gray-800">
            <i class="fas fa-globe mr-2"></i>
            <span>English</span>
            <i class="fas fa-chevron-down ml-1 text-xs"></i>
        </button>
        <div class="language-dropdown hidden absolute right-0 mt-2 w-40 bg-white rounded-md shadow-lg z-20">
            <div class="py-1">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">English</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Español</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">中文</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">العربية</a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex flex-col md:flex-row w-full max-w-4xl rounded-xl overflow-hidden logout-card">
        <!-- Left Side - Branding -->
        <div class="gradient-bg text-white p-8 md:p-12 md:w-2/5 flex flex-col justify-center items-center text-center">
            <div class="fade-in mb-8">
                <div class="flex items-center justify-center mb-6">
                    <i class="fas fa-book-open text-4xl mr-3"></i>
                    <h1 class="text-3xl font-bold">English Language Center</h1>
                </div>
                <p class="text-lg opacity-90 mb-8">Master English with our expert teachers and comprehensive curriculum</p>
            </div>
            
            <div class="fade-in w-full max-w-md">
                <div class="flex items-center mb-6">
                    <div class="flex-1 border-t border-blue-300"></div>
                    <span class="px-4 text-blue-200">What's next?</span>
                    <div class="flex-1 border-t border-blue-300"></div>
                </div>
                
                <div class="grid grid-cols-1 gap-4 text-left">
                    <div class="flex items-start">
                        <i class="fas fa-bookmark text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Save Your Progress</h4>
                            <p class="text-sm opacity-80">All your progress is securely saved</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-calendar-check text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Continue Learning</h4>
                            <p class="text-sm opacity-80">Resume your studies anytime</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-chart-line text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Track Improvement</h4>
                            <p class="text-sm opacity-80">See your progress when you return</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Logout Form -->
        <div class="bg-white p-8 md:p-12 md:w-3/5 flex flex-col justify-center fade-in" style="animation-delay: 0.2s;">
            <div class="max-w-md mx-auto w-full text-center">
                <div class="w-24 h-24 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-check-circle text-5xl text-accent"></i>
                </div>
                
                <h2 class="text-3xl font-bold text-gray-800 mb-2">You've been logged out</h2>
                <p class="text-gray-600 mb-8">Thanks for using our learning platform. Your session is now secure.</p>
                
                <div class="bg-blue-50 rounded-lg p-4 mb-8 text-left">
                    <div class="flex items-start">
                        <i class="fas fa-shield-alt text-primary mt-1 mr-3"></i>
                        <div>
                            <h3 class="font-medium text-gray-800">Security Notice</h3>
                            <p class="text-sm text-gray-600">All personal information has been securely cleared from this device.</p>
                        </div>
                    </div>
                </div>
                
                <div class="flex flex-col space-y-4">
                    <a href="{% url 'login' %}" class="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Sign in again
                    </a>
                    
                    <a href="{% url 'center:dashboard' %}" class="w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-home mr-2"></i>
                        Return to homepage
                    </a>
                </div>
                
                <div class="mt-8 text-center text-sm text-gray-600">
                    <p>Need help? <a href="#" class="text-primary font-medium hover:text-secondary">Contact support</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="absolute bottom-4 left-0 right-0 text-center text-xs text-gray-500">
        <p>© {% now "Y" %} English Language Center. All rights reserved. <a href="#" class="text-primary hover:text-secondary">Privacy Policy</a> | <a href="#" class="text-primary hover:text-secondary">Terms of Service</a></p>
    </div>

    <script>
        // Confetti animation
        function createConfetti() {
            const container = document.getElementById('confetti-container');
            const colors = ['#3b82f6', '#1d4ed8', '#10b981', '#f87171', '#fbbf24'];
            
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.classList.add('confetti');
                
                // Random properties
                const size = Math.random() * 10 + 5;
                const posX = Math.random() * 100;
                const duration = Math.random() * 3 + 2;
                const delay = Math.random() * 2;
                const color = colors[Math.floor(Math.random() * colors.length)];
                
                confetti.style.width = `${size}px`;
                confetti.style.height = `${size}px`;
                confetti.style.left = `${posX}%`;
                confetti.style.backgroundColor = color;
                confetti.style.animationDuration = `${duration}s`;
                confetti.style.animationDelay = `${delay}s`;
                
                container.appendChild(confetti);
                
                // Remove confetti after animation completes
                setTimeout(() => {
                    confetti.remove();
                }, (duration + delay) * 1000);
            }
        }
        
        // Trigger confetti on page load
        setTimeout(createConfetti, 300);
        
        // Language selector toggle
        document.querySelector('.language-selector').addEventListener('click', function(e) {
            e.stopPropagation();
            document.querySelector('.language-dropdown').classList.toggle('hidden');
        });
        
        // Close language dropdown when clicking outside
        document.addEventListener('click', function() {
            document.querySelector('.language-dropdown').classList.add('hidden');
        });
        
        // Auto-confetti every 5 seconds
        setInterval(createConfetti, 5000);
    </script>
</body>
</html>
