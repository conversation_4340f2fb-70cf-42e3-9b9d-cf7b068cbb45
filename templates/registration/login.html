<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Language Center - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1d4ed8',
                        accent: '#10b981',
                        dark: '#1e293b',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .fade-in {
            animation: fadeIn 0.6s ease-out forwards;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        .input-focus:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
        }
        .login-card {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
    <!-- Main Content -->
    <div class="flex flex-col md:flex-row w-full max-w-6xl rounded-xl overflow-hidden login-card">
        <!-- Left Side - Branding -->
        <div class="gradient-bg text-white p-8 md:p-12 md:w-1/2 flex flex-col justify-center items-center text-center">
            <div class="fade-in mb-8">
                <div class="flex items-center justify-center mb-6">
                    <i class="fas fa-book-open text-4xl mr-3"></i>
                    <h1 class="text-3xl font-bold">English Language Center</h1>
                </div>
                <p class="text-lg opacity-90 mb-8">Master English with our expert teachers and comprehensive curriculum</p>
            </div>
            
            <div class="fade-in w-full max-w-md">
                <div class="flex items-center mb-6">
                    <div class="flex-1 border-t border-blue-300"></div>
                    <span class="px-4 text-blue-200">Why choose us?</span>
                    <div class="flex-1 border-t border-blue-300"></div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Certified Teachers</h4>
                            <p class="text-sm opacity-80">Native speakers with TEFL certification</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Flexible Schedule</h4>
                            <p class="text-sm opacity-80">Morning, afternoon and evening classes</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Small Classes</h4>
                            <p class="text-sm opacity-80">Maximum 15 students per class</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-check-circle text-accent mt-1 mr-2"></i>
                        <div>
                            <h4 class="font-medium">Modern Methods</h4>
                            <p class="text-sm opacity-80">Interactive and engaging lessons</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Side - Login Form -->
        <div class="bg-white p-8 md:p-12 md:w-1/2 flex flex-col justify-center">
            <div class="fade-in">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">Welcome Back!</h2>
                    <p class="text-gray-600">Please sign in to access the management system</p>
                </div>
                
                {% if form.errors %}
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                        {% for field, errors in form.errors.items %}
                            {% for error in errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        {% endfor %}
                    </div>
                {% endif %}
                
                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    <div>
                        <label for="{{ form.username.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                            <i class="fas fa-user mr-2 text-gray-400"></i>Username
                        </label>
                        <input type="text" 
                               name="{{ form.username.name }}" 
                               id="{{ form.username.id_for_label }}"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition duration-300"
                               placeholder="Enter your username"
                               value="{{ form.username.value|default:'' }}"
                               required>
                    </div>
                    
                    <div>
                        <label for="{{ form.password.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                            <i class="fas fa-lock mr-2 text-gray-400"></i>Password
                        </label>
                        <div class="relative">
                            <input type="password" 
                                   name="{{ form.password.name }}" 
                                   id="{{ form.password.id_for_label }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none input-focus transition duration-300"
                                   placeholder="Enter your password"
                                   required>
                            <button type="button" class="absolute right-3 top-3 text-gray-400 hover:text-gray-600" onclick="togglePassword()">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-primary focus:ring-primary">
                            <span class="ml-2 text-gray-600">Remember me</span>
                        </label>
                        <a href="#" class="text-primary hover:text-secondary transition duration-300">Forgot password?</a>
                    </div>
                    
                    <button type="submit" class="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-secondary transition duration-300 transform hover:scale-105">
                        <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                    </button>
                </form>
                
                <div class="mt-8 text-center">
                    <p class="text-gray-600">Need help? Contact your administrator</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('{{ form.password.id_for_label }}');
            const toggleIcon = document.getElementById('toggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
