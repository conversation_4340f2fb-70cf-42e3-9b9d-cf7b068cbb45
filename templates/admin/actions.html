{% load i18n admin_urls static admin_list %}

<div class="actions" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #ddd;">
    {% if actions_on_top and cl.show_admin_actions %}
        {% admin_actions %}
    {% endif %}
    {% if action_form and actions_on_top and cl.show_admin_actions %}
        <form action="" method="post" novalidate style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
            {% csrf_token %}
            <div class="action-counter" data-actions-icnt="{{ cl.result_count }}" style="color: #495057; font-weight: 500;">
                <span class="action-counter-text">{{ cl.result_count }} of {{ cl.result_count }} selected</span>
            </div>
            
            <div style="display: flex; align-items: center; gap: 10px;">
                <label for="{{ action_form.action.id_for_label }}" style="color: #495057; font-weight: 500; margin: 0;">
                    <i class="fas fa-bolt" style="margin-right: 5px; color: #667eea;"></i>
                    Action:
                </label>
                {{ action_form.action }}
                <button type="submit" class="button" name="index" value="{{ action_index|default:0 }}" 
                        style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; 
                               color: white !important; 
                               border: none !important; 
                               padding: 8px 16px !important; 
                               border-radius: 5px !important; 
                               cursor: pointer !important;
                               transition: all 0.3s !important;
                               display: flex !important;
                               align-items: center !important;
                               gap: 5px !important;">
                    <i class="fas fa-play"></i>
                    {% trans "Go" %}
                </button>
            </div>
            
            {% if action_form.select_across %}
                <div class="select-across" style="color: #495057;">
                    {{ action_form.select_across }}
                    <label for="{{ action_form.select_across.id_for_label }}" style="margin-left: 5px;">
                        {% blocktrans with cl.result_count as total_count %}Select all {{ total_count }} {{ module_name }}{% endblocktrans %}
                    </label>
                </div>
            {% endif %}
        </form>
    {% endif %}
</div>

<style>
.actions button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

.actions select {
    padding: 8px 12px !important;
    border: 1px solid #ddd !important;
    border-radius: 5px !important;
    background: white !important;
    color: #495057 !important;
    font-size: 14px !important;
    transition: all 0.3s !important;
}

.actions select:focus {
    outline: none !important;
    border-color: #667eea !important;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

.action-counter {
    background: white !important;
    padding: 8px 12px !important;
    border-radius: 5px !important;
    border: 1px solid #ddd !important;
    font-size: 14px !important;
}

.select-across {
    background: #e3f2fd !important;
    padding: 8px 12px !important;
    border-radius: 5px !important;
    border: 1px solid #bbdefb !important;
    font-size: 14px !important;
}

.select-across input[type="checkbox"] {
    margin-right: 5px !important;
}

@media (max-width: 768px) {
    .actions form {
        flex-direction: column !important;
        align-items: stretch !important;
    }
    
    .actions form > div {
        justify-content: center !important;
    }
}

/* Enhanced action messages */
.action-messages {
    margin-top: 15px !important;
}

.action-messages .success {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 5px !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.action-messages .success::before {
    content: "\f00c" !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
}

.action-messages .error {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 5px !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.action-messages .error::before {
    content: "\f00d" !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
}

.action-messages .warning {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%) !important;
    color: white !important;
    padding: 12px 20px !important;
    border-radius: 5px !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.action-messages .warning::before {
    content: "\f071" !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
}

/* Action confirmation styling */
.action-confirmation {
    background: #fff3cd !important;
    border: 1px solid #ffeaa7 !important;
    border-radius: 8px !important;
    padding: 20px !important;
    margin: 20px 0 !important;
}

.action-confirmation h2 {
    color: #856404 !important;
    margin-bottom: 15px !important;
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.action-confirmation h2::before {
    content: "\f071" !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    color: #f39c12 !important;
}

.action-confirmation ul {
    background: white !important;
    border-radius: 5px !important;
    padding: 15px !important;
    margin: 15px 0 !important;
    border: 1px solid #ddd !important;
    max-height: 200px !important;
    overflow-y: auto !important;
}

.action-confirmation ul li {
    padding: 5px 0 !important;
    border-bottom: 1px solid #eee !important;
}

.action-confirmation ul li:last-child {
    border-bottom: none !important;
}

.action-confirmation .submit-row {
    background: none !important;
    padding: 0 !important;
    margin-top: 20px !important;
    display: flex !important;
    gap: 10px !important;
    justify-content: flex-end !important;
}

.action-confirmation .submit-row input[type="submit"] {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%) !important;
    color: white !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: 5px !important;
    cursor: pointer !important;
    font-weight: 500 !important;
    transition: all 0.3s !important;
}

.action-confirmation .submit-row input[type="submit"]:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

.action-confirmation .submit-row .cancel-link {
    background: #6c757d !important;
    color: white !important;
    text-decoration: none !important;
    padding: 12px 24px !important;
    border-radius: 5px !important;
    transition: all 0.3s !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 5px !important;
}

.action-confirmation .submit-row .cancel-link:hover {
    background: #5a6268 !important;
    transform: translateY(-1px) !important;
}
</style>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
