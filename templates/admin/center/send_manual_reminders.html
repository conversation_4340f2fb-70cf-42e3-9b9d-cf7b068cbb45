{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}Send Manual Payment Reminders{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">Home</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
    &rsaquo; <a href="{% url 'admin:center_paymentremindersettings_changelist' %}">Payment Reminder Settings</a>
    &rsaquo; Send Manual Reminders
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>Send Manual Payment Reminders</h1>
    
    <div class="form-row">
        <div class="help">
            <p>This will send payment reminder emails to students with upcoming or overdue payments. Review the list below before sending.</p>
        </div>
    </div>
    
    {% if total_emails > 0 %}
        <div class="module">
            <h2>📧 Payment Reminders to Send ({{ students_to_remind|length }})</h2>
            {% if students_to_remind %}
                <table>
                    <thead>
                        <tr>
                            <th>Student</th>
                            <th>Email</th>
                            <th>Amount</th>
                            <th>Due Date</th>
                            <th>Days Until Due</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in students_to_remind %}
                        <tr>
                            <td>{{ item.student.full_name }}</td>
                            <td>{{ item.student.email }}</td>
                            <td>${{ item.payment.amount }}</td>
                            <td>{{ item.payment.next_due_date|date:"M d, Y" }}</td>
                            <td>{{ item.days_until_due }} day{{ item.days_until_due|pluralize }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p>No payment reminders need to be sent.</p>
            {% endif %}
        </div>
        
        <div class="module">
            <h2>🚨 Overdue Notifications to Send ({{ students_overdue|length }})</h2>
            {% if students_overdue %}
                <table>
                    <thead>
                        <tr>
                            <th>Student</th>
                            <th>Email</th>
                            <th>Amount</th>
                            <th>Due Date</th>
                            <th>Days Overdue</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in students_overdue %}
                        <tr style="background-color: #ffe6e6;">
                            <td>{{ item.student.full_name }}</td>
                            <td>{{ item.student.email }}</td>
                            <td>${{ item.payment.amount }}</td>
                            <td>{{ item.payment.next_due_date|date:"M d, Y" }}</td>
                            <td>{{ item.days_overdue }} day{{ item.days_overdue|pluralize }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <p>No overdue notifications need to be sent.</p>
            {% endif %}
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="submit-row">
                <input type="submit" value="Send {{ total_emails }} Email{{ total_emails|pluralize }}" 
                       class="default" name="_send"
                       onclick="return confirm('Are you sure you want to send {{ total_emails }} email{{ total_emails|pluralize }}?')">
                <a href="{% url 'admin:center_paymentremindersettings_changelist' %}" class="button cancel-link">Cancel</a>
            </div>
        </form>
    {% else %}
        <div class="module">
            <h2>✅ No Emails to Send</h2>
            <p>There are no payment reminders or overdue notifications that need to be sent at this time.</p>
            <p>This could mean:</p>
            <ul>
                <li>All students have paid their fees on time</li>
                <li>Reminder emails have already been sent today</li>
                <li>No payments are due in the configured reminder periods</li>
            </ul>
        </div>
        
        <div class="submit-row">
            <a href="{% url 'admin:center_paymentremindersettings_changelist' %}" class="button">Back to Settings</a>
        </div>
    {% endif %}
    
    <div class="module">
        <h2>ℹ️ Reminder Schedule</h2>
        <p><strong>Payment Reminders:</strong> Sent 7, 3, and 1 days before due date</p>
        <p><strong>Overdue Notifications:</strong> Sent 1, 7, and 14 days after due date</p>
        <p><strong>Note:</strong> Duplicate emails are automatically prevented - each student will only receive one email per day for each payment.</p>
    </div>
</div>

<style>
.cancel-link {
    margin-left: 10px;
    padding: 10px 15px;
    background: #f8f8f8;
    border: 1px solid #ddd;
    text-decoration: none;
    color: #333;
    border-radius: 4px;
}
.cancel-link:hover {
    background: #e8e8e8;
    text-decoration: none;
}
table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}
table th, table td {
    padding: 8px 12px;
    border: 1px solid #ddd;
    text-align: left;
}
table th {
    background-color: #f8f8f8;
    font-weight: bold;
}
table tr:nth-child(even) {
    background-color: #f9f9f9;
}
</style>
{% endblock %}
