{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block title %}Send Test Email{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">Home</a>
    &rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
    &rsaquo; <a href="{% url 'admin:center_paymentremindersettings_changelist' %}">Payment Reminder Settings</a>
    &rsaquo; Send Test Email
</div>
{% endblock %}

{% block content %}
<div class="module aligned">
    <h1>Send Test Email</h1>
    
    <div class="form-row">
        <div class="help">
            <p>Use this form to send a test email to verify that your email configuration is working correctly.</p>
        </div>
    </div>
    
    <form method="post">
        {% csrf_token %}
        
        <fieldset class="module aligned">
            <div class="form-row">
                <div>
                    <label for="test_email" class="required">Test Email Address:</label>
                    <input type="email" name="test_email" id="test_email" class="vTextField" required 
                           placeholder="Enter email address to send test email">
                    <div class="help">Enter the email address where you want to send the test email.</div>
                </div>
            </div>
        </fieldset>
        
        <div class="submit-row">
            <input type="submit" value="Send Test Email" class="default" name="_send">
            <a href="{% url 'admin:center_paymentremindersettings_changelist' %}" class="button cancel-link">Cancel</a>
        </div>
    </form>
    
    <div class="module">
        <h2>Email Configuration Status</h2>
        <table>
            <tr>
                <th>Setting</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Email Backend</td>
                <td>{{ settings.EMAIL_BACKEND }}</td>
            </tr>
            <tr>
                <td>Email Host</td>
                <td>{{ settings.EMAIL_HOST|default:"Not configured" }}</td>
            </tr>
            <tr>
                <td>Email Port</td>
                <td>{{ settings.EMAIL_PORT|default:"Not configured" }}</td>
            </tr>
            <tr>
                <td>Use TLS</td>
                <td>{{ settings.EMAIL_USE_TLS|yesno:"Yes,No,Not configured" }}</td>
            </tr>
            <tr>
                <td>From Email</td>
                <td>{{ settings.DEFAULT_FROM_EMAIL|default:"Not configured" }}</td>
            </tr>
        </table>
    </div>
</div>

<style>
.cancel-link {
    margin-left: 10px;
    padding: 10px 15px;
    background: #f8f8f8;
    border: 1px solid #ddd;
    text-decoration: none;
    color: #333;
    border-radius: 4px;
}
.cancel-link:hover {
    background: #e8e8e8;
    text-decoration: none;
}
</style>
{% endblock %}
