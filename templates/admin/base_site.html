{% extends "admin/base.html" %}

{% block title %}{{ title }} | Language Center Admin{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}" style="color: #007cba; text-decoration: none; display: flex; align-items: center;">
        <i class="fas fa-graduation-cap" style="margin-right: 10px; font-size: 1.5em;"></i>
        <span>Language Center</span>
    </a>
</h1>
{% endblock %}

{% block nav-global %}
<div id="nav-global" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 10px 0;">
    <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; padding: 0 20px;">
        <!-- Navigation Links -->
        <div style="display: flex; gap: 20px;">
            <a href="{% url 'admin:index' %}" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 5px; transition: all 0.3s;">
                <i class="fas fa-home" style="margin-right: 5px;"></i>
                Dashboard
            </a>
            <a href="{% url 'admin:center_student_changelist' %}" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 5px; transition: all 0.3s;">
                <i class="fas fa-user-graduate" style="margin-right: 5px;"></i>
                Students
            </a>
            <a href="{% url 'admin:center_teacher_changelist' %}" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 5px; transition: all 0.3s;">
                <i class="fas fa-chalkboard-teacher" style="margin-right: 5px;"></i>
                Teachers
            </a>
            <a href="{% url 'admin:center_class_changelist' %}" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 5px; transition: all 0.3s;">
                <i class="fas fa-users" style="margin-right: 5px;"></i>
                Classes
            </a>
            <a href="{% url 'admin:center_payment_changelist' %}" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 5px; transition: all 0.3s;">
                <i class="fas fa-coins" style="margin-right: 5px;"></i>
                Payments
            </a>
            <a href="{% url 'admin:center_emailnotification_changelist' %}" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 5px; transition: all 0.3s;">
                <i class="fas fa-envelope" style="margin-right: 5px;"></i>
                Notifications
            </a>
        </div>

        <!-- User Info -->
        <div style="color: white; display: flex; align-items: center; gap: 15px;">
            <span style="opacity: 0.9;">
                <i class="fas fa-user" style="margin-right: 5px;"></i>
                Welcome, {{ user.get_full_name|default:user.username }}
            </span>
            <a href="{% url 'admin:logout' %}" style="color: white; text-decoration: none; padding: 8px 15px; border-radius: 5px; background: rgba(255,255,255,0.2); transition: all 0.3s;">
                <i class="fas fa-sign-out-alt" style="margin-right: 5px;"></i>
                Logout
            </a>
        </div>
    </div>
</div>

<style>
#nav-global a:hover {
    background: rgba(255,255,255,0.2) !important;
    transform: translateY(-1px);
}

/* Enhanced admin styling */
.module h2, .module caption, .inline-group h2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

.button, input[type=submit], input[type=button], .submit-row input, a.button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    color: white !important;
    padding: 10px 20px !important;
    border-radius: 5px !important;
    transition: all 0.3s !important;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover, a.button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

.button.default, input[type=submit].default, .submit-row input.default {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
}

.button.default:hover, input[type=submit].default:hover, .submit-row input.default:hover {
    background: linear-gradient(135deg, #38f9d7 0%, #43e97b 100%) !important;
}

/* Table styling */
#result_list th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 2px solid #667eea !important;
}

#result_list tr:nth-child(even) {
    background: #f8f9fa !important;
}

#result_list tr:hover {
    background: #e3f2fd !important;
    transition: all 0.3s !important;
}

/* Form styling */
.form-row {
    border-bottom: 1px solid #eee !important;
    padding: 15px 0 !important;
}

.form-row:last-child {
    border-bottom: none !important;
}

/* Fieldset styling */
fieldset.module {
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
}

fieldset.module h2 {
    border-radius: 8px 8px 0 0 !important;
    margin: 0 !important;
    padding: 15px 20px !important;
}

/* Messages styling */
.messagelist li {
    border-radius: 5px !important;
    margin-bottom: 10px !important;
    padding: 15px 20px !important;
}

.messagelist .success {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    color: white !important;
}

.messagelist .error {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
    color: white !important;
}

.messagelist .warning {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%) !important;
    color: white !important;
}

/* Responsive design */
@media (max-width: 768px) {
    #nav-global > div {
        flex-direction: column !important;
        gap: 10px !important;
    }
    
    #nav-global > div > div {
        flex-wrap: wrap !important;
        justify-content: center !important;
    }
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced breadcrumbs */
.breadcrumbs {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-bottom: 1px solid #ddd !important;
    padding: 15px 20px !important;
    border-radius: 5px !important;
    margin-bottom: 20px !important;
}

.breadcrumbs a {
    color: #667eea !important;
    text-decoration: none !important;
}

.breadcrumbs a:hover {
    text-decoration: underline !important;
}

/* Enhanced pagination */
.paginator {
    text-align: center !important;
    margin: 20px 0 !important;
}

.paginator a, .paginator .this-page {
    display: inline-block !important;
    padding: 8px 12px !important;
    margin: 0 2px !important;
    border-radius: 5px !important;
    text-decoration: none !important;
    transition: all 0.3s !important;
}

.paginator a {
    background: #f8f9fa !important;
    color: #667eea !important;
    border: 1px solid #ddd !important;
}

.paginator a:hover {
    background: #667eea !important;
    color: white !important;
    transform: translateY(-1px) !important;
}

.paginator .this-page {
    background: #667eea !important;
    color: white !important;
    border: 1px solid #667eea !important;
}

/* Enhanced filters */
#changelist-filter {
    background: #f8f9fa !important;
    border-radius: 8px !important;
    padding: 20px !important;
}

#changelist-filter h2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 10px 15px !important;
    border-radius: 5px !important;
    margin-bottom: 15px !important;
}

#changelist-filter h3 {
    color: #667eea !important;
    border-bottom: 1px solid #ddd !important;
    padding-bottom: 5px !important;
    margin-bottom: 10px !important;
}

#changelist-filter a {
    color: #495057 !important;
    text-decoration: none !important;
    padding: 5px 10px !important;
    border-radius: 3px !important;
    display: block !important;
    transition: all 0.3s !important;
}

#changelist-filter a:hover {
    background: #e9ecef !important;
    color: #667eea !important;
}

#changelist-filter a.selected {
    background: #667eea !important;
    color: white !important;
}
</style>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}
