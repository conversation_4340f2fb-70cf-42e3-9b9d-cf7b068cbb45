{% extends "admin/base_site.html" %}
{% load i18n static admin_urls %}

{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">
        <i class="fas fa-graduation-cap" style="margin-right: 10px; color: #007cba;"></i>
        Language Center Administration
    </a>
</h1>
{% endblock %}

{% block content %}
<div class="dashboard-container" style="margin: 20px 0;">
    <!-- Statistics Cards -->
    <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
        <!-- Students Card -->
        <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <h3 style="margin: 0; font-size: 2.5em; font-weight: bold;">{{ total_students|default:0 }}</h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">Active Students</p>
                </div>
                <i class="fas fa-user-graduate" style="font-size: 3em; opacity: 0.7;"></i>
            </div>
        </div>

        <!-- Teachers Card -->
        <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <h3 style="margin: 0; font-size: 2.5em; font-weight: bold;">{{ total_teachers|default:0 }}</h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">Active Teachers</p>
                </div>
                <i class="fas fa-chalkboard-teacher" style="font-size: 3em; opacity: 0.7;"></i>
            </div>
        </div>

        <!-- Classes Card -->
        <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <h3 style="margin: 0; font-size: 2.5em; font-weight: bold;">{{ total_classes|default:0 }}</h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">Active Classes</p>
                </div>
                <i class="fas fa-users" style="font-size: 3em; opacity: 0.7;"></i>
            </div>
        </div>

        <!-- Revenue Card -->
        <div class="stat-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <h3 style="margin: 0; font-size: 2.5em; font-weight: bold;">{{ total_revenue|floatformat:0|default:0 }}</h3>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">Total Revenue (MAD)</p>
                </div>
                <i class="fas fa-coins" style="font-size: 3em; opacity: 0.7;"></i>
            </div>
        </div>
    </div>

    <!-- Alerts Section -->
    {% if overdue_payments or upcoming_payments or failed_emails %}
    <div class="alerts-section" style="margin-bottom: 30px;">
        <h2 style="color: #333; margin-bottom: 15px;">
            <i class="fas fa-exclamation-triangle" style="color: #f39c12; margin-right: 10px;"></i>
            Alerts & Notifications
        </h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
            {% if overdue_payments %}
            <div style="background: #fee; border-left: 4px solid #e74c3c; padding: 15px; border-radius: 5px;">
                <h4 style="color: #e74c3c; margin: 0 0 5px 0;">
                    <i class="fas fa-exclamation-circle"></i> Overdue Payments
                </h4>
                <p style="margin: 0;">{{ overdue_payments }} payment(s) are overdue</p>
                <a href="{% url 'admin:center_payment_changelist' %}?is_confirmed__exact=0&next_due_date__lt={{ today }}" 
                   style="color: #e74c3c; text-decoration: none; font-weight: bold;">View Details →</a>
            </div>
            {% endif %}

            {% if upcoming_payments %}
            <div style="background: #fff3cd; border-left: 4px solid #f39c12; padding: 15px; border-radius: 5px;">
                <h4 style="color: #f39c12; margin: 0 0 5px 0;">
                    <i class="fas fa-clock"></i> Upcoming Payments
                </h4>
                <p style="margin: 0;">{{ upcoming_payments }} payment(s) due within 7 days</p>
                <a href="{% url 'admin:center_payment_changelist' %}?is_confirmed__exact=0" 
                   style="color: #f39c12; text-decoration: none; font-weight: bold;">View Details →</a>
            </div>
            {% endif %}

            {% if failed_emails %}
            <div style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; border-radius: 5px;">
                <h4 style="color: #dc3545; margin: 0 0 5px 0;">
                    <i class="fas fa-envelope-open-text"></i> Failed Emails
                </h4>
                <p style="margin: 0;">{{ failed_emails }} email(s) failed to send</p>
                <a href="{% url 'admin:center_emailnotification_changelist' %}?status__exact=FAILED" 
                   style="color: #dc3545; text-decoration: none; font-weight: bold;">View Details →</a>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="quick-actions" style="margin-bottom: 30px;">
        <h2 style="color: #333; margin-bottom: 15px;">
            <i class="fas fa-bolt" style="color: #3498db; margin-right: 10px;"></i>
            Quick Actions
        </h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <a href="{% url 'admin:center_student_add' %}" 
               style="background: #3498db; color: white; padding: 15px; border-radius: 8px; text-decoration: none; text-align: center; transition: all 0.3s;">
                <i class="fas fa-user-plus" style="display: block; font-size: 2em; margin-bottom: 10px;"></i>
                Add New Student
            </a>
            <a href="{% url 'admin:center_teacher_add' %}" 
               style="background: #e74c3c; color: white; padding: 15px; border-radius: 8px; text-decoration: none; text-align: center; transition: all 0.3s;">
                <i class="fas fa-chalkboard-teacher" style="display: block; font-size: 2em; margin-bottom: 10px;"></i>
                Add New Teacher
            </a>
            <a href="{% url 'admin:center_class_add' %}" 
               style="background: #f39c12; color: white; padding: 15px; border-radius: 8px; text-decoration: none; text-align: center; transition: all 0.3s;">
                <i class="fas fa-users" style="display: block; font-size: 2em; margin-bottom: 10px;"></i>
                Add New Class
            </a>
            <a href="{% url 'admin:center_payment_add' %}" 
               style="background: #27ae60; color: white; padding: 15px; border-radius: 8px; text-decoration: none; text-align: center; transition: all 0.3s;">
                <i class="fas fa-coins" style="display: block; font-size: 2em; margin-bottom: 10px;"></i>
                Add Payment
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="recent-activity" style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
        <!-- Recent Students -->
        <div>
            <h3 style="color: #333; margin-bottom: 15px;">
                <i class="fas fa-user-graduate" style="color: #3498db; margin-right: 10px;"></i>
                Recent Students
            </h3>
            <div style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden;">
                {% for student in recent_students %}
                <div style="padding: 15px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong>{{ student.full_name }}</strong><br>
                        <small style="color: #666;">{{ student.get_level_display }} • {{ student.registration_date|date:"M d, Y" }}</small>
                    </div>
                    <a href="{% url 'admin:center_student_change' student.pk %}" 
                       style="color: #3498db; text-decoration: none;">
                        <i class="fas fa-edit"></i>
                    </a>
                </div>
                {% empty %}
                <div style="padding: 20px; text-align: center; color: #666;">
                    No recent students
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Recent Payments -->
        <div>
            <h3 style="color: #333; margin-bottom: 15px;">
                <i class="fas fa-coins" style="color: #27ae60; margin-right: 10px;"></i>
                Recent Payments
            </h3>
            <div style="background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden;">
                {% for payment in recent_payments %}
                <div style="padding: 15px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong>{{ payment.student.full_name }}</strong><br>
                        <small style="color: #666;">{{ payment.amount }} MAD • {{ payment.payment_date|date:"M d, Y" }}</small>
                    </div>
                    <a href="{% url 'admin:center_payment_change' payment.pk %}" 
                       style="color: #27ae60; text-decoration: none;">
                        <i class="fas fa-edit"></i>
                    </a>
                </div>
                {% empty %}
                <div style="padding: 20px; text-align: center; color: #666;">
                    No recent payments
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Default Django Admin Content -->
<div id="content-main">
    {% if app_list %}
        {% for app in app_list %}
            <div class="app-{{ app.app_label }} module">
                <table>
                    <caption>
                        <a href="{{ app.app_url }}" class="section" title="{% blocktrans with name=app.name %}Models in the {{ name }} application{% endblocktrans %}">{{ app.name }}</a>
                    </caption>
                    {% for model in app.models %}
                        <tr class="model-{{ model.object_name|lower }}">
                            {% if model.admin_url %}
                                <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
                            {% else %}
                                <th scope="row">{{ model.name }}</th>
                            {% endif %}

                            {% if model.add_url %}
                                <td><a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a></td>
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}

                            {% if model.admin_url %}
                                {% if model.view_only %}
                                    <td><a href="{{ model.admin_url }}" class="viewlink">{% trans 'View' %}</a></td>
                                {% else %}
                                    <td><a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a></td>
                                {% endif %}
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </table>
            </div>
        {% endfor %}
    {% else %}
        <p>{% trans "You don't have permission to view or edit anything." %}</p>
    {% endif %}
</div>

<style>
.quick-actions a:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.stat-card:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr !important;
    }
    .recent-activity {
        grid-template-columns: 1fr !important;
    }
    .quick-actions {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}
</style>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}
