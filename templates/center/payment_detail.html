{% extends 'center/base.html' %}

{% block title %}Payment Details - English Language Center{% endblock %}
{% block page_title %}Payment Details{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Payment Header -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-2xl font-bold">Payment #{{ payment.id }}</h1>
                <p class="text-gray-500">{{ payment.student.full_name }}</p>
                <div class="flex items-center mt-2">
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm mr-3">${{ payment.amount }}</span>
                    {% if payment.is_confirmed %}
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Confirmed</span>
                    {% else %}
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">Pending</span>
                    {% endif %}
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'center:payment_update' payment.pk %}" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    <i class="fas fa-edit mr-2"></i> Edit
                </a>
                <a href="{% url 'center:payment_list' %}" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Payment Information -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4">Payment Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-500 text-sm">Student</label>
                        <p class="font-medium">
                            <a href="{% url 'center:student_detail' payment.student.pk %}" class="text-primary hover:text-secondary">
                                {{ payment.student.full_name }}
                            </a>
                        </p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Amount</label>
                        <p class="font-medium text-green-600">${{ payment.amount }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Payment Date</label>
                        <p class="font-medium">{{ payment.payment_date|date:"F d, Y H:i" }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Payment Method</label>
                        <p class="font-medium">{{ payment.get_payment_method_display }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Next Due Date</label>
                        <p class="font-medium">
                            {{ payment.next_due_date|date:"F d, Y" }}
                            {% if payment.is_overdue %}
                                <span class="text-red-500 text-sm">(Overdue)</span>
                            {% elif payment.days_until_due <= 7 %}
                                <span class="text-yellow-500 text-sm">(Due Soon)</span>
                            {% endif %}
                        </p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Status</label>
                        <p class="font-medium">
                            {% if payment.is_confirmed %}
                                <span class="text-green-600">Confirmed</span>
                            {% else %}
                                <span class="text-yellow-600">Pending Confirmation</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                {% if payment.notes %}
                <div class="mt-6">
                    <label class="block text-gray-500 text-sm mb-2">Notes</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-700">{{ payment.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Student Information -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Student Information</h3>
                <div class="flex items-center">
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16 mr-4 flex items-center justify-center">
                        <i class="fas fa-user-graduate text-gray-400 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">
                            <a href="{% url 'center:student_detail' payment.student.pk %}" class="text-primary hover:text-secondary">
                                {{ payment.student.full_name }}
                            </a>
                        </h4>
                        <p class="text-sm text-gray-500">{{ payment.student.email }}</p>
                        <p class="text-sm text-gray-500">{{ payment.student.get_level_display }} Level</p>
                        <p class="text-sm text-gray-500">Phone: {{ payment.student.phone_number }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Payment Status -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Payment Status</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500">Amount</span>
                        <span class="font-bold text-green-600">${{ payment.amount }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Status</span>
                        <span class="font-bold">
                            {% if payment.is_confirmed %}
                                <span class="text-green-600">Confirmed</span>
                            {% else %}
                                <span class="text-yellow-600">Pending</span>
                            {% endif %}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Days Until Due</span>
                        <span class="font-bold">
                            {% if payment.is_overdue %}
                                <span class="text-red-600">Overdue</span>
                            {% elif payment.days_until_due <= 7 %}
                                <span class="text-yellow-600">{{ payment.days_until_due }} days</span>
                            {% else %}
                                <span class="text-green-600">{{ payment.days_until_due }} days</span>
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Student Payment Summary -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Student Summary</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500">Total Paid</span>
                        <span class="font-bold text-green-600">${{ payment.student.total_paid|floatformat:2 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Pending</span>
                        <span class="font-bold text-yellow-600">${{ payment.student.pending_payments|floatformat:2 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Total Payments</span>
                        <span class="font-bold">{{ payment.student.payments.count }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Actions</h3>
                <div class="space-y-2">
                    {% if not payment.is_confirmed %}
                        <button class="block w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg text-center hover:bg-green-200 transition duration-300">
                            <i class="fas fa-check mr-2"></i> Confirm Payment
                        </button>
                    {% endif %}
                    <a href="{% url 'center:payment_update' payment.pk %}" class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg text-center hover:bg-blue-200 transition duration-300">
                        <i class="fas fa-edit mr-2"></i> Edit Payment
                    </a>
                    <button class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-center hover:bg-gray-200 transition duration-300">
                        <i class="fas fa-print mr-2"></i> Print Receipt
                    </button>
                    <a href="{% url 'center:student_detail' payment.student.pk %}" class="block w-full px-4 py-2 bg-purple-100 text-purple-700 rounded-lg text-center hover:bg-purple-200 transition duration-300">
                        <i class="fas fa-user mr-2"></i> View Student
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
