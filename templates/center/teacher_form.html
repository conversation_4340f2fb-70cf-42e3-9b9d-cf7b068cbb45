{% extends 'center/base.html' %}

{% block title %}{% if object %}Edit Teacher{% else %}Add Teacher{% endif %} - English Language Center{% endblock %}
{% block page_title %}{% if object %}Edit Teacher{% else %}Add New Teacher{% endif %}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold">{% if object %}Edit Teacher: {{ object.name }}{% else %}Add New Teacher{% endif %}</h3>
            <a href="{% url 'center:teacher_list' %}" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </a>
        </div>
        
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.name.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Full Name <span class="text-red-500">*</span>
                    </label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.name.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.email.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.phone_number.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Phone Number <span class="text-red-500">*</span>
                    </label>
                    {{ form.phone_number }}
                    {% if form.phone_number.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.phone_number.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.subject_specialization.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Subject Specialization <span class="text-red-500">*</span>
                    </label>
                    {{ form.subject_specialization }}
                    {% if form.subject_specialization.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.subject_specialization.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="flex items-center">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-gray-700 font-medium">
                    Active Teacher
                </label>
            </div>
            
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'center:teacher_list' %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    {% if object %}Update Teacher{% else %}Save Teacher{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
