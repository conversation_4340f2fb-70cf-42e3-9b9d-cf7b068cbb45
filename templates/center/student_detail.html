{% extends 'center/base.html' %}

{% block title %}{{ student.full_name }} - Student Details{% endblock %}
{% block page_title %}Student Profile{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Student Header -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
        <div class="flex justify-between items-start">
            <div class="flex items-center">
                <div class="bg-gray-200 border-2 border-dashed rounded-xl w-20 h-20 mr-6 flex items-center justify-center">
                    <i class="fas fa-user-graduate text-gray-400 text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold">{{ student.full_name }}</h1>
                    <p class="text-gray-500">{{ student.email }}</p>
                    <div class="flex items-center mt-2">
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm mr-3">{{ student.get_level_display }}</span>
                        {% if student.is_active %}
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Active</span>
                        {% else %}
                            <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">Inactive</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'center:student_update' student.pk %}" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    <i class="fas fa-edit mr-2"></i> Edit
                </a>
                <a href="{% url 'center:student_list' %}" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal Information -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4">Personal Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-500 text-sm">Full Name</label>
                        <p class="font-medium">{{ student.full_name }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Email</label>
                        <p class="font-medium">{{ student.email }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Phone Number</label>
                        <p class="font-medium">{{ student.phone_number }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Gender</label>
                        <p class="font-medium">{{ student.get_gender_display }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Date of Birth</label>
                        <p class="font-medium">{{ student.date_of_birth|date:"F d, Y" }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Age</label>
                        <p class="font-medium">{{ student.age }} years old</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Registration Date</label>
                        <p class="font-medium">{{ student.registration_date|date:"F d, Y" }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Current Level</label>
                        <p class="font-medium">{{ student.get_level_display }}</p>
                    </div>
                </div>
            </div>

            <!-- Enrolled Classes -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Enrolled Classes</h3>
                {% if student.classes.all %}
                    <div class="space-y-3">
                        {% for class in student.classes.all %}
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div>
                                <h4 class="font-medium">{{ class.class_name }}</h4>
                                <p class="text-sm text-gray-500">{{ class.get_level_display }} • {{ class.teacher.name }}</p>
                                <p class="text-sm text-gray-500">{{ class.schedule_display }}</p>
                            </div>
                            <div class="text-right">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                    {{ class.current_enrollment }}/{{ class.capacity }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-8">Not enrolled in any classes</p>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Stats</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500">Total Paid</span>
                        <span class="font-bold text-green-600">${{ student.total_paid|floatformat:2 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Pending</span>
                        <span class="font-bold text-yellow-600">${{ student.pending_payments|floatformat:2 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Classes</span>
                        <span class="font-bold">{{ student.classes.count }}</span>
                    </div>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">Recent Payments</h3>
                    <a href="{% url 'center:payment_create' %}?student={{ student.pk }}" class="text-primary text-sm hover:text-secondary">
                        Add Payment
                    </a>
                </div>
                {% if student.payments.all %}
                    <div class="space-y-3">
                        {% for payment in student.payments.all|slice:":5" %}
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="font-medium">${{ payment.amount }}</p>
                                <p class="text-sm text-gray-500">{{ payment.payment_date|date:"M d, Y" }}</p>
                            </div>
                            <div class="text-right">
                                {% if payment.is_confirmed %}
                                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Confirmed</span>
                                {% else %}
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Pending</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-4">No payments recorded</p>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Actions</h3>
                <div class="space-y-2">
                    <a href="{% url 'center:payment_create' %}?student={{ student.pk }}" class="block w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg text-center hover:bg-green-200 transition duration-300">
                        <i class="fas fa-dollar-sign mr-2"></i> Add Payment
                    </a>
                    <a href="mailto:{{ student.email }}" class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg text-center hover:bg-blue-200 transition duration-300">
                        <i class="fas fa-envelope mr-2"></i> Send Email
                    </a>
                    <a href="{% url 'center:student_update' student.pk %}" class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-center hover:bg-gray-200 transition duration-300">
                        <i class="fas fa-edit mr-2"></i> Edit Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
