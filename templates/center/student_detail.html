{% extends 'center/base.html' %}

{% block title %}{{ student.full_name }} - Student Details{% endblock %}
{% block page_title %}Student Profile{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Student Header -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
        <div class="flex justify-between items-start">
            <div class="flex items-center">
                <div class="bg-gray-200 border-2 border-dashed rounded-xl w-20 h-20 mr-6 flex items-center justify-center">
                    <i class="fas fa-user-graduate text-gray-400 text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold">{{ student.full_name }}</h1>
                    <p class="text-gray-500">{{ student.email }}</p>
                    <div class="flex items-center mt-2">
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm mr-3">{{ student.get_level_display }}</span>
                        {% if student.is_active %}
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Active</span>
                        {% else %}
                            <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">Inactive</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'center:student_update' student.pk %}" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    <i class="fas fa-edit mr-2"></i> Edit
                </a>
                <a href="{% url 'center:student_delete' student.pk %}" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300" onclick="return confirmDelete('student', '{{ student.full_name }}')">
                    <i class="fas fa-trash mr-2"></i> Delete
                </a>
                <a href="{% url 'center:student_list' %}" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal Information -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4">Personal Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-500 text-sm">Full Name</label>
                        <p class="font-medium">{{ student.full_name }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Email</label>
                        <p class="font-medium">{{ student.email }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Phone Number</label>
                        <p class="font-medium">{{ student.phone_number }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Gender</label>
                        <p class="font-medium">{{ student.get_gender_display }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Date of Birth</label>
                        <p class="font-medium">{{ student.date_of_birth|date:"F d, Y" }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Age</label>
                        <p class="font-medium">{{ student.age }} years old</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Registration Date</label>
                        <p class="font-medium">{{ student.registration_date|date:"F d, Y" }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Current Level</label>
                        <p class="font-medium">{{ student.get_level_display }}</p>
                    </div>
                </div>
            </div>

            <!-- Enrolled Classes -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Enrolled Classes</h3>
                {% if student.classes.all %}
                    <div class="space-y-3">
                        {% for class in student.classes.all %}
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div>
                                <h4 class="font-medium">{{ class.class_name }}</h4>
                                <p class="text-sm text-gray-500">{{ class.get_level_display }} • {{ class.teacher.name }}</p>
                                <p class="text-sm text-gray-500">{{ class.schedule_display }}</p>
                            </div>
                            <div class="text-right">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                    {{ class.current_enrollment }}/{{ class.capacity }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-8">Not enrolled in any classes</p>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Stats</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500">Total Paid</span>
                        <span class="font-bold text-green-600">{{ student.total_paid|floatformat:2 }} MAD</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Pending</span>
                        <span class="font-bold text-yellow-600">{{ student.pending_payments|floatformat:2 }} MAD</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Classes</span>
                        <span class="font-bold">{{ student.classes.count }}</span>
                    </div>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">Recent Payments</h3>
                    <a href="{% url 'center:payment_create' %}?student={{ student.pk }}" class="text-primary text-sm hover:text-secondary">
                        Add Payment
                    </a>
                </div>
                {% if student.payments.all %}
                    <div class="space-y-3">
                        {% for payment in student.payments.all|slice:":5" %}
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="font-medium">{{ payment.amount }} MAD</p>
                                <p class="text-sm text-gray-500">{{ payment.payment_date|date:"M d, Y" }}</p>
                            </div>
                            <div class="text-right">
                                {% if payment.is_confirmed %}
                                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Confirmed</span>
                                {% else %}
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Pending</span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-4">No payments recorded</p>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Actions</h3>
                <div class="space-y-2">
                    <button onclick="openClassEnrollmentModal()" class="block w-full px-4 py-2 bg-purple-100 text-purple-700 rounded-lg text-center hover:bg-purple-200 transition duration-300">
                        <i class="fas fa-graduation-cap mr-2"></i> Enroll in Class
                    </button>
                    <a href="{% url 'center:payment_create' %}?student={{ student.pk }}" class="block w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg text-center hover:bg-green-200 transition duration-300">
                        <i class="fas fa-coins mr-2"></i> Add Payment
                    </a>
                    <a href="mailto:{{ student.email }}" class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg text-center hover:bg-blue-200 transition duration-300">
                        <i class="fas fa-envelope mr-2"></i> Send Email
                    </a>
                    <a href="{% url 'center:student_update' student.pk %}" class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-center hover:bg-gray-200 transition duration-300">
                        <i class="fas fa-edit mr-2"></i> Edit Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Class Enrollment Modal -->
<div id="classEnrollmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-96 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-500 to-blue-600">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-white">
                        <i class="fas fa-graduation-cap mr-2"></i>Enroll {{ student.full_name }} in Class
                    </h3>
                    <button onclick="closeClassEnrollmentModal()" class="text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <!-- Filter -->
                <div class="mb-4">
                    <div class="flex space-x-3">
                        <select id="classLevelFilter" class="flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                onchange="loadAvailableClasses()">
                            <option value="">All Levels</option>
                            <option value="{{ student.level }}" selected>{{ student.get_level_display }} (Student's Level)</option>
                            <option value="BEGINNER">Beginner</option>
                            <option value="ELEMENTARY">Elementary</option>
                            <option value="INTERMEDIATE">Intermediate</option>
                            <option value="UPPER_INTERMEDIATE">Upper-Intermediate</option>
                            <option value="ADVANCED">Advanced</option>
                            <option value="PROFICIENCY">Proficiency</option>
                        </select>
                    </div>
                    <div class="text-sm text-gray-600 mt-2">
                        <i class="fas fa-info-circle mr-1"></i>
                        Student level: {{ student.get_level_display }} | Currently enrolled in {{ student.classes.count }} class{{ student.classes.count|pluralize:"es" }}
                    </div>
                </div>

                <!-- Classes List -->
                <div id="classesContainer" class="max-h-64 overflow-y-auto">
                    <div id="classLoadingSpinner" class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-gray-400 text-2xl"></i>
                        <p class="text-gray-500 mt-2">Loading classes...</p>
                    </div>
                    <div id="classesList" class="space-y-2 hidden"></div>
                    <div id="noClasses" class="text-center py-8 hidden">
                        <i class="fas fa-chalkboard text-gray-300 text-3xl"></i>
                        <p class="text-gray-500 mt-2">No available classes found</p>
                    </div>
                </div>

                <!-- Enrollment Form -->
                <form id="classEnrollmentForm" method="post" class="hidden mt-4">
                    {% csrf_token %}
                    <input type="hidden" id="selectedClassId" name="class_id">
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeClassEnrollmentModal()"
                                class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
                            <i class="fas fa-graduation-cap mr-2"></i>Enroll in Class
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let availableClasses = [];
let selectedClass = null;

function openClassEnrollmentModal() {
    document.getElementById('classEnrollmentModal').classList.remove('hidden');
    loadAvailableClasses();
}

function closeClassEnrollmentModal() {
    document.getElementById('classEnrollmentModal').classList.add('hidden');
    document.getElementById('classLevelFilter').value = '{{ student.level }}';
    selectedClass = null;
    document.getElementById('classEnrollmentForm').classList.add('hidden');
}

function loadAvailableClasses() {
    const container = document.getElementById('classesContainer');
    const loading = document.getElementById('classLoadingSpinner');
    const classesList = document.getElementById('classesList');
    const noClasses = document.getElementById('noClasses');

    loading.classList.remove('hidden');
    classesList.classList.add('hidden');
    noClasses.classList.add('hidden');

    const level = document.getElementById('classLevelFilter').value;

    // Get enrolled class IDs
    const enrolledClassIds = [{% for class in student.classes.all %}{{ class.id }}{% if not forloop.last %},{% endif %}{% endfor %}];

    // Fetch all active classes
    fetch(`{% url 'center:class_list' %}`)
        .then(response => response.text())
        .then(html => {
            // This is a simple approach - in a real app, you'd want a proper API endpoint
            // For now, we'll create a simple list of available classes
            const classes = [
                {% for class in all_classes %}
                {
                    id: {{ class.id }},
                    name: '{{ class.class_name }}',
                    level: '{{ class.level }}',
                    level_display: '{{ class.get_level_display }}',
                    teacher: '{{ class.teacher.name }}',
                    schedule: '{{ class.schedule_display }}',
                    current_enrollment: {{ class.current_enrollment }},
                    capacity: {{ class.capacity }},
                    is_full: {{ class.is_full|yesno:"true,false" }},
                    available_spots: {{ class.available_spots }}
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ];

            // Filter out enrolled classes and apply level filter
            availableClasses = classes.filter(cls => {
                const notEnrolled = !enrolledClassIds.includes(cls.id);
                const levelMatch = !level || cls.level === level;
                const notFull = !cls.is_full;
                return notEnrolled && levelMatch && notFull;
            });

            displayClasses();
            loading.classList.add('hidden');
        })
        .catch(error => {
            console.error('Error loading classes:', error);
            loading.classList.add('hidden');
            noClasses.classList.remove('hidden');
        });
}

function displayClasses() {
    const classesList = document.getElementById('classesList');
    const noClasses = document.getElementById('noClasses');

    if (availableClasses.length === 0) {
        classesList.classList.add('hidden');
        noClasses.classList.remove('hidden');
        return;
    }

    classesList.innerHTML = '';
    availableClasses.forEach(cls => {
        const classDiv = document.createElement('div');
        classDiv.className = 'flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-purple-50 cursor-pointer transition duration-200';
        classDiv.onclick = () => selectClass(cls);

        const levelMatch = cls.level === '{{ student.level }}';
        const levelBadgeClass = levelMatch ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';

        classDiv.innerHTML = `
            <div class="flex items-center">
                <div class="bg-gray-200 rounded-full w-8 h-8 mr-3 flex items-center justify-center">
                    <i class="fas fa-chalkboard text-gray-400 text-sm"></i>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900">${cls.name}</h4>
                    <p class="text-sm text-gray-500">${cls.teacher} • ${cls.schedule}</p>
                    <p class="text-xs text-gray-400">${cls.current_enrollment}/${cls.capacity} students</p>
                </div>
            </div>
            <div class="text-right">
                <span class="px-2 py-1 ${levelBadgeClass} rounded-full text-xs">
                    ${cls.level_display}
                </span>
                ${levelMatch ? '<i class="fas fa-check-circle text-green-500 ml-2" title="Level matches student"></i>' : ''}
                <div class="text-xs text-gray-500 mt-1">${cls.available_spots} spots left</div>
            </div>
        `;

        classesList.appendChild(classDiv);
    });

    classesList.classList.remove('hidden');
    noClasses.classList.add('hidden');
}

function selectClass(cls) {
    selectedClass = cls;
    document.getElementById('selectedClassId').value = cls.id;

    // Update form action
    document.getElementById('classEnrollmentForm').action = `/center/classes/${cls.id}/enroll/`;

    // Highlight selected class
    document.querySelectorAll('#classesList > div').forEach(div => {
        div.classList.remove('bg-purple-100', 'border-purple-300');
        div.classList.add('border-gray-200');
    });

    event.currentTarget.classList.add('bg-purple-100', 'border-purple-300');
    event.currentTarget.classList.remove('border-gray-200');

    document.getElementById('classEnrollmentForm').classList.remove('hidden');
}

// Close modal when clicking outside
document.getElementById('classEnrollmentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeClassEnrollmentModal();
    }
});
</script>

{% endblock %}
