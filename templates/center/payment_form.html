{% extends 'center/base.html' %}

{% block title %}{% if object %}Edit Payment{% else %}Add Payment{% endif %} - English Language Center{% endblock %}
{% block page_title %}{% if object %}Edit Payment{% else %}Add New Payment{% endif %}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold">{% if object %}Edit Payment{% else %}Add New Payment{% endif %}</h3>
            <a href="{% url 'center:payment_list' %}" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </a>
        </div>
        
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.student.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Student <span class="text-red-500">*</span>
                    </label>
                    {{ form.student }}
                    {% if form.student.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.student.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.amount.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Amount <span class="text-red-500">*</span>
                    </label>
                    {{ form.amount }}
                    {% if form.amount.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.amount.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.payment_date.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Payment Date <span class="text-red-500">*</span>
                    </label>
                    {{ form.payment_date }}
                    {% if form.payment_date.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.payment_date.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.payment_method.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Payment Method <span class="text-red-500">*</span>
                    </label>
                    {{ form.payment_method }}
                    {% if form.payment_method.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.payment_method.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.next_due_date.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Next Due Date <span class="text-red-500">*</span>
                    </label>
                    {{ form.next_due_date }}
                    {% if form.next_due_date.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.next_due_date.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div class="flex items-center">
                    {{ form.is_confirmed }}
                    <label for="{{ form.is_confirmed.id_for_label }}" class="ml-2 text-gray-700 font-medium">
                        Payment Confirmed
                    </label>
                </div>
            </div>
            
            <div>
                <label for="{{ form.notes.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                    Notes
                </label>
                {{ form.notes }}
                {% if form.notes.errors %}
                    <p class="text-red-500 text-sm mt-1">{{ form.notes.errors.0 }}</p>
                {% endif %}
            </div>
            
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'center:payment_list' %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    {% if object %}Update Payment{% else %}Save Payment{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
