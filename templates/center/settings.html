{% extends 'center/base.html' %}

{% block title %}Settings - English Language Center{% endblock %}
{% block page_title %}System Settings{% endblock %}

{% block extra_css %}
.setting-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
.active-tab {
    border-bottom: 3px solid #3b82f6;
    color: #3b82f6;
}
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}
.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}
input:checked + .slider {
    background-color: #3b82f6;
}
input:checked + .slider:before {
    transform: translateX(26px);
}
.tab-content {
    display: none;
}
.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}
.avatar-upload {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}
.avatar-upload img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}
.avatar-upload .edit-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #3b82f6;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-4">
    <a href="{% url 'center:settings_edit' %}" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
        <i class="fas fa-edit mr-2"></i>Edit Settings
    </a>
    <div class="relative">
        <input type="text" placeholder="Search settings..." class="bg-gray-100 rounded-full py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-primary">
        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Settings Tabs -->
<div class="bg-white rounded-xl shadow-md p-6 mb-6">
    <div class="border-b border-gray-200">
        <nav class="flex space-x-8 overflow-x-auto">
            <button class="py-4 px-1 border-b-2 font-medium text-sm active-tab whitespace-nowrap" onclick="openTab('general')">
                <i class="fas fa-cog mr-2"></i> General
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('school')">
                <i class="fas fa-school mr-2"></i> School Info
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('payment')">
                <i class="fas fa-credit-card mr-2"></i> Payment
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('email')">
                <i class="fas fa-envelope mr-2"></i> Email & SMTP
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('notifications')">
                <i class="fas fa-bell mr-2"></i> Notifications
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('security')">
                <i class="fas fa-shield-alt mr-2"></i> Security
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('integrations')">
                <i class="fas fa-plug mr-2"></i> Integrations
            </button>
        </nav>
    </div>
</div>

<!-- General Settings Tab -->
<div id="general" class="tab-content active">
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
        <h3 class="font-bold text-lg mb-6">General Settings</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- System Preferences -->
            <div class="setting-card p-6 border border-gray-200 rounded-lg transition duration-300">
                <h4 class="font-semibold mb-4 flex items-center">
                    <i class="fas fa-desktop mr-2 text-primary"></i> System Preferences
                </h4>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">Dark Mode</p>
                            <p class="text-sm text-gray-500">Switch between light and dark theme</p>
                        </div>
                        <label class="switch">
                            <input type="checkbox">
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">Language</p>
                            <p class="text-sm text-gray-500">System interface language</p>
                        </div>
                        <select class="bg-gray-100 border-0 rounded-lg py-1 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                            <option>English</option>
                            <option>Spanish</option>
                            <option>French</option>
                            <option>German</option>
                        </select>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">Time Zone</p>
                            <p class="text-sm text-gray-500">Set your local time zone</p>
                        </div>
                        <select class="bg-gray-100 border-0 rounded-lg py-1 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                            <option>(UTC-05:00) Eastern Time</option>
                            <option>(UTC-06:00) Central Time</option>
                            <option>(UTC-07:00) Mountain Time</option>
                            <option>(UTC-08:00) Pacific Time</option>
                        </select>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">Date Format</p>
                            <p class="text-sm text-gray-500">How dates are displayed</p>
                        </div>
                        <select class="bg-gray-100 border-0 rounded-lg py-1 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                            <option>MM/DD/YYYY</option>
                            <option>DD/MM/YYYY</option>
                            <option>YYYY-MM-DD</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- User Profile -->
            <div class="setting-card p-6 border border-gray-200 rounded-lg transition duration-300">
                <h4 class="font-semibold mb-4 flex items-center">
                    <i class="fas fa-user mr-2 text-primary"></i> User Profile
                </h4>
                
                <div class="flex flex-col items-center mb-6">
                    <div class="avatar-upload mb-4">
                        <img src="https://via.placeholder.com/120" alt="Profile" id="profileImage">
                        <div class="edit-icon" onclick="document.getElementById('profileUpload').click()">
                            <i class="fas fa-pencil-alt"></i>
                        </div>
                        <input type="file" id="profileUpload" accept="image/*" class="hidden">
                    </div>
                    <h5 class="font-bold">{{ user.get_full_name|default:user.username }}</h5>
                    <p class="text-sm text-gray-500">{{ user.email }}</p>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-700 mb-1">Full Name</label>
                        <input type="text" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="{{ user.get_full_name|default:user.username }}">
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">Email</label>
                        <input type="email" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="{{ user.email }}">
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">Phone Number</label>
                        <input type="tel" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="+****************">
                    </div>
                    
                    <button class="w-full mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition">
                        Update Profile
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- School Info Tab -->
<div id="school" class="tab-content">
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
        <h3 class="font-bold text-lg mb-6">School Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="setting-card p-6 border border-gray-200 rounded-lg transition duration-300">
                <h4 class="font-semibold mb-4 flex items-center">
                    <i class="fas fa-info-circle mr-2 text-primary"></i> Basic Information
                </h4>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-700 mb-1">School Name</label>
                        <input type="text" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="English Language Center">
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">School Logo</label>
                        <div class="flex items-center">
                            <div class="mr-4">
                                <img src="https://via.placeholder.com/80" alt="School Logo" class="w-20 h-20 object-contain border rounded-lg">
                            </div>
                            <div>
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition">
                                    Upload Logo
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">Description</label>
                        <textarea class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" rows="3">Master English with our expert teachers and comprehensive curriculum</textarea>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="setting-card p-6 border border-gray-200 rounded-lg transition duration-300">
                <h4 class="font-semibold mb-4 flex items-center">
                    <i class="fas fa-address-book mr-2 text-primary"></i> Contact Information
                </h4>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-700 mb-1">Address</label>
                        <textarea class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" rows="2">123 Education Street, Learning City, LC 12345</textarea>
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">Phone Number</label>
                        <input type="tel" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="+****************">
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">Email</label>
                        <input type="email" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="<EMAIL>">
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">Website</label>
                        <input type="url" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="https://www.englishcenter.com">
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-6 flex justify-end">
            <button class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition">
                Save Changes
            </button>
        </div>
    </div>
</div>

<!-- Other tabs content would go here -->
<div id="payment" class="tab-content">
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="font-bold text-lg mb-6">Payment Settings</h3>
        <p class="text-gray-500">Payment configuration options will be available here.</p>
    </div>
</div>

<div id="notifications" class="tab-content">
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="font-bold text-lg mb-6">Notification Settings</h3>
        <p class="text-gray-500">Notification preferences will be available here.</p>
    </div>
</div>

<div id="security" class="tab-content">
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="font-bold text-lg mb-6">Security Settings</h3>
        <p class="text-gray-500">Security and privacy options will be available here.</p>
    </div>
</div>

<div id="integrations" class="tab-content">
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="font-bold text-lg mb-6">Integrations</h3>
        <p class="text-gray-500">Third-party integrations will be available here.</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
function openTab(tabName) {
    // Hide all tab contents
    var tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(function(content) {
        content.classList.remove('active');
    });
    
    // Remove active class from all tabs
    var tabs = document.querySelectorAll('nav button');
    tabs.forEach(function(tab) {
        tab.classList.remove('active-tab');
        tab.classList.add('border-transparent', 'text-gray-500');
        tab.classList.remove('border-primary', 'text-primary');
    });
    
    // Show selected tab content
    document.getElementById(tabName).classList.add('active');
    
    // Add active class to clicked tab
    event.target.classList.add('active-tab');
    event.target.classList.remove('border-transparent', 'text-gray-500');
    event.target.classList.add('border-primary', 'text-primary');
}

// Profile image upload
document.getElementById('profileUpload').addEventListener('change', function(e) {
    if (e.target.files && e.target.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('profileImage').src = e.target.result;
        };
        reader.readAsDataURL(e.target.files[0]);
    }
});
{% endblock %}
