{% extends 'center/base.html' %}

{% block title %}Settings - English Language Center{% endblock %}
{% block page_title %}System Settings{% endblock %}

{% block extra_css %}
.setting-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
.active-tab {
    border-bottom: 3px solid #3b82f6;
    color: #3b82f6;
}
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}
.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}
input:checked + .slider {
    background-color: #3b82f6;
}
input:checked + .slider:before {
    transform: translateX(26px);
}
.tab-content {
    display: none;
}
.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}
.avatar-upload {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}
.avatar-upload img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}
.avatar-upload .edit-icon {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #3b82f6;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-4">
    <a href="{% url 'center:settings_edit' %}" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
        <i class="fas fa-edit mr-2"></i>Edit Settings
    </a>
    <div class="relative">
        <input type="text" placeholder="Search settings..." class="bg-gray-100 rounded-full py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-primary">
        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
    </div>
</div>
{% endblock %}

{% block content %}
{% csrf_token %}
<!-- Settings Header -->
<div class="bg-white rounded-xl shadow-md p-6 mb-6">
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">System Settings</h2>
            <p class="text-gray-600 mt-1">Configure your language center settings and preferences</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'center:settings_edit' %}" class="bg-primary hover:bg-secondary text-white px-6 py-2 rounded-lg transition duration-200 flex items-center">
                <i class="fas fa-edit mr-2"></i>Edit Settings
            </a>
            <button onclick="testEmailConnection()" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition duration-200 flex items-center">
                <i class="fas fa-envelope-open mr-2"></i>Test Email
            </button>
        </div>
    </div>
</div>

<!-- Settings Tabs -->
<div class="bg-white rounded-xl shadow-md p-6 mb-6">
    <div class="border-b border-gray-200">
        <nav class="flex space-x-8 overflow-x-auto">
            <button class="py-4 px-1 border-b-2 font-medium text-sm active-tab whitespace-nowrap" onclick="openTab('general')">
                <i class="fas fa-cog mr-2"></i> General
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('school')">
                <i class="fas fa-school mr-2"></i> School Info
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('payment')">
                <i class="fas fa-credit-card mr-2"></i> Payment
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('email')">
                <i class="fas fa-envelope mr-2"></i> Email & SMTP
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('notifications')">
                <i class="fas fa-bell mr-2"></i> Notifications
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('security')">
                <i class="fas fa-shield-alt mr-2"></i> Security
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap" onclick="openTab('integrations')">
                <i class="fas fa-plug mr-2"></i> Integrations
            </button>
        </nav>
    </div>
</div>

<!-- General Settings Tab -->
<div id="general" class="tab-content active">
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
        <h3 class="font-bold text-lg mb-6">General Settings</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- System Preferences -->
            <div class="setting-card p-6 border border-gray-200 rounded-lg transition duration-300">
                <h4 class="font-semibold mb-4 flex items-center">
                    <i class="fas fa-desktop mr-2 text-primary"></i> System Preferences
                </h4>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">Dark Mode</p>
                            <p class="text-sm text-gray-500">Switch between light and dark theme</p>
                        </div>
                        <label class="switch">
                            <input type="checkbox">
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">Language</p>
                            <p class="text-sm text-gray-500">System interface language</p>
                        </div>
                        <select class="bg-gray-100 border-0 rounded-lg py-1 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                            <option>English</option>
                            <option>Spanish</option>
                            <option>French</option>
                            <option>German</option>
                        </select>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">Time Zone</p>
                            <p class="text-sm text-gray-500">Set your local time zone</p>
                        </div>
                        <select class="bg-gray-100 border-0 rounded-lg py-1 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                            <option>(UTC-05:00) Eastern Time</option>
                            <option>(UTC-06:00) Central Time</option>
                            <option>(UTC-07:00) Mountain Time</option>
                            <option>(UTC-08:00) Pacific Time</option>
                        </select>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="font-medium">Date Format</p>
                            <p class="text-sm text-gray-500">How dates are displayed</p>
                        </div>
                        <select class="bg-gray-100 border-0 rounded-lg py-1 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                            <option>MM/DD/YYYY</option>
                            <option>DD/MM/YYYY</option>
                            <option>YYYY-MM-DD</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- User Profile -->
            <div class="setting-card p-6 border border-gray-200 rounded-lg transition duration-300">
                <h4 class="font-semibold mb-4 flex items-center">
                    <i class="fas fa-user mr-2 text-primary"></i> User Profile
                </h4>
                
                <div class="flex flex-col items-center mb-6">
                    <div class="avatar-upload mb-4">
                        <img src="https://via.placeholder.com/120" alt="Profile" id="profileImage">
                        <div class="edit-icon" onclick="document.getElementById('profileUpload').click()">
                            <i class="fas fa-pencil-alt"></i>
                        </div>
                        <input type="file" id="profileUpload" accept="image/*" class="hidden">
                    </div>
                    <h5 class="font-bold">{{ user.get_full_name|default:user.username }}</h5>
                    <p class="text-sm text-gray-500">{{ user.email }}</p>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-700 mb-1">Full Name</label>
                        <input type="text" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="{{ user.get_full_name|default:user.username }}">
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">Email</label>
                        <input type="email" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="{{ user.email }}">
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">Phone Number</label>
                        <input type="tel" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="+****************">
                    </div>
                    
                    <button class="w-full mt-4 px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition">
                        Update Profile
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- School Info Tab -->
<div id="school" class="tab-content">
    <form method="post" action="#" onsubmit="saveSchoolInfo(event)">
        {% csrf_token %}
        <div class="bg-white rounded-xl shadow-md p-6 mb-6">
            <h3 class="font-bold text-lg mb-6">School Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="setting-card p-6 border border-gray-200 rounded-lg transition duration-300">
                <h4 class="font-semibold mb-4 flex items-center">
                    <i class="fas fa-info-circle mr-2 text-primary"></i> Basic Information
                </h4>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-700 mb-1">School Name</label>
                        <input type="text" name="school_name" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="duolinguainstitute Center">
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">School Logo</label>
                        <div class="flex items-center">
                            <div class="mr-4">
                                <img src="https://via.placeholder.com/80" alt="School Logo" class="w-20 h-20 object-contain border rounded-lg">
                            </div>
                            <div>
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition">
                                    Upload Logo
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-gray-700 mb-1">Description</label>
                        <textarea name="school_description" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" rows="3">Master English with our expert teachers and comprehensive curriculum</textarea>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="setting-card p-6 border border-gray-200 rounded-lg transition duration-300">
                <h4 class="font-semibold mb-4 flex items-center">
                    <i class="fas fa-address-book mr-2 text-primary"></i> Contact Information
                </h4>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-700 mb-1">Address</label>
                        <textarea name="school_address" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" rows="2">bureau n° 5, Rue lima, Rte de Sefrou, Fes 30000</textarea>
                    </div>

                    <div>
                        <label class="block text-gray-700 mb-1">Phone Number</label>
                        <input type="tel" name="school_phone" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="+212 676864826">
                    </div>

                    <div>
                        <label class="block text-gray-700 mb-1">Email</label>
                        <input type="email" name="school_email" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="<EMAIL>">
                    </div>

                    <div>
                        <label class="block text-gray-700 mb-1">Website</label>
                        <input type="url" name="school_website" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary" value="https://www.duolinguainstitute.com">
                    </div>
                </div>
            </div>
        </div>
        
            <div class="mt-6 flex justify-end">
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition">
                    <i class="fas fa-save mr-2"></i>Save Changes
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Email & SMTP Settings Tab -->
<div id="email" class="tab-content">
    <form method="post" class="space-y-6">
        {% csrf_token %}

        {% if messages %}
            {% for message in messages %}
                <div class="p-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                    <i class="fas fa-info-circle mr-2"></i>{{ message }}
                </div>
            {% endfor %}
        {% endif %}

        {% if form.non_field_errors %}
            <div class="p-4 rounded-lg bg-red-100 text-red-700">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
        {% endif %}

        <!-- Email Configuration -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-purple-600">
                <h2 class="text-xl font-semibold text-white">
                    <i class="fas fa-envelope mr-2"></i>Email Configuration
                </h2>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-toggle-on mr-2"></i>Enable Email Notifications
                        </label>
                        {{ form.email_enabled }}
                        {% if form.email_enabled.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.email_enabled.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-envelope mr-2"></i>From Email Address
                        </label>
                        {{ form.from_email }}
                        {% if form.from_email.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.from_email.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-reply mr-2"></i>Reply-To Email Address
                        </label>
                        {{ form.reply_to_email }}
                        {% if form.reply_to_email.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.reply_to_email.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- SMTP Settings -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-500 to-blue-500">
                <h2 class="text-xl font-semibold text-white">
                    <i class="fas fa-server mr-2"></i>SMTP Configuration
                </h2>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-server mr-2"></i>SMTP Host
                        </label>
                        {{ form.smtp_host }}
                        {% if form.smtp_host.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.smtp_host.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-plug mr-2"></i>SMTP Port
                        </label>
                        {{ form.smtp_port }}
                        {% if form.smtp_port.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.smtp_port.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2"></i>SMTP Username
                        </label>
                        {{ form.smtp_username }}
                        {% if form.smtp_username.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.smtp_username.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-key mr-2"></i>SMTP Password
                        </label>
                        {{ form.smtp_password }}
                        {% if form.smtp_password.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.smtp_password.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-shield-alt mr-2"></i>Use TLS
                        </label>
                        {{ form.smtp_use_tls }}
                        {% if form.smtp_use_tls.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.smtp_use_tls.errors.0 }}</p>
                        {% endif %}
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-2"></i>Use SSL
                        </label>
                        {{ form.smtp_use_ssl }}
                        {% if form.smtp_use_ssl.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.smtp_use_ssl.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Reminder Settings -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-500 to-pink-500">
                <h2 class="text-xl font-semibold text-white">
                    <i class="fas fa-bell mr-2"></i>Payment Reminder Settings
                </h2>
            </div>
            <div class="p-6 space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-calendar-alt mr-2"></i>Reminder Days Before Due
                        </label>
                        {{ form.reminder_days_before }}
                        {% if form.reminder_days_before.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.reminder_days_before.errors.0 }}</p>
                        {% endif %}
                        <p class="text-gray-500 text-xs mt-1">Comma-separated days (e.g., 7,3,1)</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-exclamation-triangle mr-2"></i>Overdue Reminder Days
                        </label>
                        {{ form.overdue_reminder_days }}
                        {% if form.overdue_reminder_days.errors %}
                            <p class="text-red-500 text-sm mt-1">{{ form.overdue_reminder_days.errors.0 }}</p>
                        {% endif %}
                        <p class="text-gray-500 text-xs mt-1">Comma-separated days after due date (e.g., 1,7,14)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex space-x-4">
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition duration-200">
                <i class="fas fa-save mr-2"></i>Save Settings
            </button>
            <button type="button" onclick="resetEmailSettings()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg transition duration-200">
                <i class="fas fa-undo mr-2"></i>Reset to Defaults
            </button>
        </div>
    </form>
</div>

<!-- Other tabs content would go here -->
<div id="payment" class="tab-content">
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="font-bold text-lg mb-6">Payment Settings</h3>
        <p class="text-gray-500">Payment configuration options will be available here.</p>
    </div>
</div>

<div id="notifications" class="tab-content">
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="font-bold text-lg mb-6">Notification Settings</h3>
        <p class="text-gray-500">Notification preferences will be available here.</p>
    </div>
</div>

<div id="security" class="tab-content">
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="font-bold text-lg mb-6">Security Settings</h3>
        <p class="text-gray-500">Security and privacy options will be available here.</p>
    </div>
</div>

<div id="integrations" class="tab-content">
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="font-bold text-lg mb-6">Integrations</h3>
        <p class="text-gray-500">Third-party integrations will be available here.</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
function openTab(tabName) {
    // Hide all tab contents
    var tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(function(content) {
        content.classList.remove('active');
    });
    
    // Remove active class from all tabs
    var tabs = document.querySelectorAll('nav button');
    tabs.forEach(function(tab) {
        tab.classList.remove('active-tab');
        tab.classList.add('border-transparent', 'text-gray-500');
        tab.classList.remove('border-primary', 'text-primary');
    });
    
    // Show selected tab content
    document.getElementById(tabName).classList.add('active');
    
    // Add active class to clicked tab
    event.target.classList.add('active-tab');
    event.target.classList.remove('border-transparent', 'text-gray-500');
    event.target.classList.add('border-primary', 'text-primary');
}

// Profile image upload
document.getElementById('profileUpload').addEventListener('change', function(e) {
    if (e.target.files && e.target.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('profileImage').src = e.target.result;
        };
        reader.readAsDataURL(e.target.files[0]);
    }
});

// Test email connection
function testEmailConnection() {
    // Get test email address
    const testEmail = prompt('Enter email address to send test email to:');
    if (!testEmail) return;

    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';
    button.disabled = true;

    // Send AJAX request
    fetch('{% url "center:test_email_settings" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `test_email=${encodeURIComponent(testEmail)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ Error: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Reset email settings to defaults
function resetEmailSettings() {
    if (confirm('Are you sure you want to reset email settings to their default values?')) {
        // Reset SMTP settings to defaults
        document.querySelector('input[name="smtp_host"]').value = 'smtp.gmail.com';
        document.querySelector('input[name="smtp_port"]').value = '587';
        document.querySelector('input[name="smtp_username"]').value = '';
        document.querySelector('input[name="smtp_password"]').value = '';
        document.querySelector('input[name="smtp_use_tls"]').checked = true;
        document.querySelector('input[name="smtp_use_ssl"]').checked = false;

        // Reset email settings
        document.querySelector('input[name="from_email"]').value = '<EMAIL>';
        document.querySelector('input[name="reply_to_email"]').value = '';
        document.querySelector('input[name="email_enabled"]').checked = true;

        // Reset reminder settings
        document.querySelector('input[name="reminder_days_before"]').value = '7,3,1';
        document.querySelector('input[name="overdue_reminder_days"]').value = '1,7,14';
    }
}

// Handle school info form submission
function saveSchoolInfo(event) {
    event.preventDefault();

    // Get form data
    const formData = new FormData(event.target);
    const schoolData = {};
    for (let [key, value] of formData.entries()) {
        if (key.startsWith('school_')) {
            schoolData[key] = value;
        }
    }

    // Store in localStorage for now (you can implement backend storage later)
    localStorage.setItem('schoolInfo', JSON.stringify(schoolData));

    // Show success message
    const successDiv = document.createElement('div');
    successDiv.className = 'p-4 rounded-lg bg-green-100 text-green-700 mb-4';
    successDiv.innerHTML = '<i class="fas fa-check-circle mr-2"></i>School information saved successfully!';

    const schoolTab = document.getElementById('school');
    schoolTab.insertBefore(successDiv, schoolTab.firstChild);

    // Remove success message after 3 seconds
    setTimeout(() => {
        successDiv.remove();
    }, 3000);
}
{% endblock %}
