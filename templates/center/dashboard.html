{% extends 'center/base.html' %}

{% block title %}Dashboard - English Language Center{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- Total Students -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                <i class="fas fa-user-graduate text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-gray-500 text-sm">Total Students</p>
                <p class="text-2xl font-bold text-gray-800">{{ total_students }}</p>
            </div>
        </div>
    </div>
    
    <!-- Total Teachers -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-chalkboard-teacher text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-gray-500 text-sm">Total Teachers</p>
                <p class="text-2xl font-bold text-gray-800">{{ total_teachers }}</p>
            </div>
        </div>
    </div>
    
    <!-- Active Classes -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                <i class="fas fa-users text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-gray-500 text-sm">Active Classes</p>
                <p class="text-2xl font-bold text-gray-800">{{ total_classes }}</p>
            </div>
        </div>
    </div>
    
    <!-- Total Revenue -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <i class="fas fa-dollar-sign text-2xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-gray-500 text-sm">Total Revenue</p>
                <p class="text-2xl font-bold text-gray-800">${{ total_revenue|floatformat:2 }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Revenue and Payment Status -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Monthly Revenue -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold mb-4">Monthly Revenue</h3>
        <div class="flex items-center justify-between">
            <div>
                <p class="text-3xl font-bold text-green-600">${{ monthly_revenue|floatformat:2 }}</p>
                <p class="text-gray-500">This month</p>
            </div>
            <div class="p-3 rounded-full bg-green-100 text-green-600">
                <i class="fas fa-chart-line text-2xl"></i>
            </div>
        </div>
    </div>
    
    <!-- Payment Alerts -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold mb-4">Payment Alerts</h3>
        <div class="space-y-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                    <span class="text-gray-700">Overdue Payments</span>
                </div>
                <span class="font-bold text-red-600">{{ overdue_payments }}</span>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                    <span class="text-gray-700">Due This Week</span>
                </div>
                <span class="font-bold text-yellow-600">{{ upcoming_payments }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities and Quick Stats -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Recent Students -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold">Recent Students</h3>
            <a href="{% url 'center:student_list' %}" class="text-primary hover:text-secondary text-sm">View All</a>
        </div>
        <div class="space-y-3">
            {% for student in recent_students %}
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10 mr-3"></div>
                    <div>
                        <p class="font-medium">{{ student.full_name }}</p>
                        <p class="text-sm text-gray-500">{{ student.level }}</p>
                    </div>
                </div>
                <span class="text-xs text-gray-500">{{ student.registration_date|date:"M d" }}</span>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No recent students</p>
            {% endfor %}
        </div>
    </div>
    
    <!-- Recent Payments -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold">Recent Payments</h3>
            <a href="{% url 'center:payment_list' %}" class="text-primary hover:text-secondary text-sm">View All</a>
        </div>
        <div class="space-y-3">
            {% for payment in recent_payments %}
            <div class="flex items-center justify-between">
                <div>
                    <p class="font-medium">{{ payment.student.full_name }}</p>
                    <p class="text-sm text-gray-500">${{ payment.amount }}</p>
                </div>
                <div class="text-right">
                    <span class="text-xs text-gray-500">{{ payment.payment_date|date:"M d" }}</span>
                    <div class="text-xs">
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full">{{ payment.payment_method }}</span>
                    </div>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No recent payments</p>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Level Distribution -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Students by Level -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold mb-4">Students by Level</h3>
        <div class="space-y-3">
            {% for level_data in students_by_level %}
            <div class="flex items-center justify-between">
                <span class="text-gray-700">{{ level_data.level|title }}</span>
                <div class="flex items-center">
                    <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                        <div class="bg-primary h-2 rounded-full" style="width: {% widthratio level_data.count total_students 100 %}%"></div>
                    </div>
                    <span class="font-bold">{{ level_data.count }}</span>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No student data available</p>
            {% endfor %}
        </div>
    </div>
    
    <!-- Classes by Level -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold mb-4">Classes by Level</h3>
        <div class="space-y-3">
            {% for class_data in classes_by_level %}
            <div class="flex items-center justify-between">
                <span class="text-gray-700">{{ class_data.level|title }}</span>
                <div class="flex items-center">
                    <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                        <div class="bg-accent h-2 rounded-full" style="width: {% widthratio class_data.count total_classes 100 %}%"></div>
                    </div>
                    <span class="font-bold">{{ class_data.count }}</span>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No class data available</p>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="fixed bottom-6 right-6 space-y-3">
    <a href="{% url 'center:student_create' %}" class="block bg-primary text-white p-3 rounded-full shadow-lg hover:bg-secondary transition duration-300">
        <i class="fas fa-user-plus text-xl"></i>
    </a>
    <a href="{% url 'center:payment_create' %}" class="block bg-accent text-white p-3 rounded-full shadow-lg hover:bg-green-600 transition duration-300">
        <i class="fas fa-dollar-sign text-xl"></i>
    </a>
</div>
{% endblock %}
