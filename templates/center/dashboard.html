{% extends 'center/base.html' %}

{% block title %}Dashboard - English Language Center{% endblock %}
{% block page_title %}Dashboard Overview{% endblock %}

{% block extra_css %}
<style>
.stat-card {
    transition: all 0.3s ease;
}
.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}
.progress-bar {
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    background-color: #e5e7eb;
}
.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.8s ease;
}
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}
.metric-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
}
.metric-up {
    background-color: #dcfce7;
    color: #166534;
}
.metric-down {
    background-color: #fef2f2;
    color: #dc2626;
}
</style>
{% endblock %}

{% block content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Students Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <p class="text-gray-500 text-sm font-medium">Total Students</p>
                <h3 class="text-3xl font-bold text-gray-900 mt-2">{{ total_students }}</h3>
                <div class="flex items-center mt-3">
                    <span class="metric-badge metric-up">
                        <i class="fas fa-arrow-up mr-1"></i> 12%
                    </span>
                    <span class="text-gray-500 text-sm ml-2">from last month</span>
                </div>
            </div>
            <div class="bg-blue-100 p-3 rounded-lg">
                <i class="fas fa-user-graduate text-blue-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Active</span>
                <span>{{ active_students|default:total_students }}</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill bg-blue-500" style="width: {% widthratio active_students|default:total_students total_students 100 %}%"></div>
            </div>
        </div>
    </div>

    <!-- Teachers Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <p class="text-gray-500 text-sm font-medium">Teachers</p>
                <h3 class="text-3xl font-bold text-gray-900 mt-2">{{ total_teachers }}</h3>
                <div class="flex items-center mt-3">
                    <span class="metric-badge metric-up">
                        <i class="fas fa-arrow-up mr-1"></i> 5%
                    </span>
                    <span class="text-gray-500 text-sm ml-2">from last month</span>
                </div>
            </div>
            <div class="bg-green-100 p-3 rounded-lg">
                <i class="fas fa-chalkboard-teacher text-green-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Active</span>
                <span>{{ active_teachers|default:total_teachers }}</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill bg-green-500" style="width: {% widthratio active_teachers|default:total_teachers total_teachers 100 %}%"></div>
            </div>
        </div>
    </div>

    <!-- Classes Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <p class="text-gray-500 text-sm font-medium">Active Classes</p>
                <h3 class="text-3xl font-bold text-gray-900 mt-2">{{ total_classes }}</h3>
                <div class="flex items-center mt-3">
                    <span class="metric-badge metric-up">
                        <i class="fas fa-arrow-up mr-1"></i> 8%
                    </span>
                    <span class="text-gray-500 text-sm ml-2">from last month</span>
                </div>
            </div>
            <div class="bg-purple-100 p-3 rounded-lg">
                <i class="fas fa-users text-purple-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Capacity</span>
                <span>82%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill bg-purple-500" style="width: 82%"></div>
            </div>
        </div>
    </div>

    <!-- Revenue Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <p class="text-gray-500 text-sm font-medium">Monthly Revenue</p>
                <h3 class="text-3xl font-bold text-gray-900 mt-2">${{ monthly_revenue|floatformat:0|default:"12,450" }}</h3>
                <div class="flex items-center mt-3">
                    <span class="metric-badge metric-up">
                        <i class="fas fa-arrow-up mr-1"></i> 15%
                    </span>
                    <span class="text-gray-500 text-sm ml-2">from last month</span>
                </div>
            </div>
            <div class="bg-yellow-100 p-3 rounded-lg">
                <i class="fas fa-dollar-sign text-yellow-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Target</span>
                <span>$15,000</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill bg-yellow-500" style="width: {% widthratio monthly_revenue|default:12450 15000 100 %}%"></div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Student Distribution Chart -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold text-gray-900">Student Distribution by Level</h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">Monthly</button>
                <button class="px-3 py-1 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">Yearly</button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="studentDistributionChart"></canvas>
        </div>
        <div class="grid grid-cols-2 gap-4 mt-6">
            <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Beginner</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Intermediate</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Upper-Intermediate</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Advanced</span>
            </div>
        </div>
    </div>

    <!-- Revenue Chart -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold text-gray-900">Revenue Overview</h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">Monthly</button>
                <button class="px-3 py-1 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">Yearly</button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="revenueChart"></canvas>
        </div>
    </div>
</div>

<!-- Additional Dashboard Sections -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Upcoming Classes -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold text-gray-900">Upcoming Classes</h3>
            <a href="{% url 'center:class_list' %}" class="text-blue-600 text-sm font-medium hover:text-blue-700">View All</a>
        </div>
        <div class="space-y-4">
            <!-- Intermediate Conversation -->
            <div class="flex items-start space-x-3">
                <div class="bg-blue-100 p-2 rounded-lg">
                    <i class="fas fa-users text-blue-600 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">Intermediate Conversation</h4>
                    <p class="text-sm text-gray-500">Mon, Wed, Fri • 10:00 AM - 11:30 AM</p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Room 201</span>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Sarah Johnson</span>
                    </div>
                </div>
            </div>

            <!-- Advanced Grammar -->
            <div class="flex items-start space-x-3">
                <div class="bg-purple-100 p-2 rounded-lg">
                    <i class="fas fa-users text-purple-600 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">Advanced Grammar</h4>
                    <p class="text-sm text-gray-500">Tue, Thu • 2:00 PM - 3:30 PM</p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Room 105</span>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Michael Chen</span>
                    </div>
                </div>
            </div>

            <!-- Beginner Writing -->
            <div class="flex items-start space-x-3">
                <div class="bg-yellow-100 p-2 rounded-lg">
                    <i class="fas fa-users text-yellow-600 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">Beginner Writing</h4>
                    <p class="text-sm text-gray-500">Sat • 9:00 AM - 12:00 PM</p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Room 302</span>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Emma Rodriguez</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold text-gray-900">Recent Payments</h3>
            <a href="{% url 'center:payment_list' %}" class="text-blue-600 text-sm font-medium hover:text-blue-700">View All</a>
        </div>
        <div class="space-y-4">
            <!-- Sarah Johnson Payment -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Sarah Johnson</h4>
                        <p class="text-sm text-gray-500">Intermediate Conversation</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$150.00</p>
                    <p class="text-xs text-gray-500">Today, 10:30 AM</p>
                </div>
            </div>

            <!-- David Wilson Payment -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">David Wilson</h4>
                        <p class="text-sm text-gray-500">Advanced Listening</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$180.00</p>
                    <p class="text-xs text-gray-500">Yesterday, 3:15 PM</p>
                </div>
            </div>

            <!-- Emma Rodriguez Payment -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Emma Rodriguez</h4>
                        <p class="text-sm text-gray-500">Beginner Writing</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$120.00</p>
                    <p class="text-xs text-gray-500">Dec 12, 2:45 PM</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-900 mb-6">Quick Actions</h3>
        <div class="grid grid-cols-2 gap-4">
            <!-- Add Student -->
            <a href="{% url 'center:student_create' %}" class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                <div class="bg-blue-100 p-3 rounded-full mb-2">
                    <i class="fas fa-user-plus text-blue-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Add Student</span>
            </a>

            <!-- Add Teacher -->
            <a href="{% url 'center:teacher_create' %}" class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                <div class="bg-green-100 p-3 rounded-full mb-2">
                    <i class="fas fa-chalkboard-teacher text-green-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Add Teacher</span>
            </a>

            <!-- Create Class -->
            <a href="{% url 'center:class_create' %}" class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                <div class="bg-purple-100 p-3 rounded-full mb-2">
                    <i class="fas fa-plus text-purple-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Create Class</span>
            </a>

            <!-- Record Payment -->
            <a href="{% url 'center:payment_create' %}" class="flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                <div class="bg-yellow-100 p-3 rounded-full mb-2">
                    <i class="fas fa-dollar-sign text-yellow-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Record Payment</span>
            </a>
        </div>
    </div>
</div>

<!-- System Status and Recent Student Registrations -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- System Status -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-900 mb-6">System Status</h3>
        <div class="space-y-4">
            <!-- Database Status -->
            <div class="flex justify-between items-center">
                <span class="text-gray-600">Database</span>
                <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full font-medium">Operational</span>
            </div>

            <!-- Server Uptime -->
            <div class="flex justify-between items-center">
                <span class="text-gray-600">Server Uptime</span>
                <span class="text-gray-900 font-medium">99.8%</span>
            </div>

            <!-- Storage -->
            <div class="flex justify-between items-center">
                <span class="text-gray-600">Storage</span>
                <span class="text-gray-900 font-medium">42% used</span>
            </div>

            <!-- Last Backup -->
            <div class="flex justify-between items-center">
                <span class="text-gray-600">Last Backup</span>
                <span class="text-gray-900 font-medium">Dec 15, 2023</span>
            </div>
        </div>
    </div>

    <!-- Recent Student Registrations -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold text-gray-900">Recent Student Registrations</h3>
            <a href="{% url 'center:student_list' %}" class="text-blue-600 text-sm font-medium hover:text-blue-700">View All Students</a>
        </div>

        <!-- Table Header -->
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                        <th class="pb-3">Student</th>
                        <th class="pb-3">Level</th>
                        <th class="pb-3">Status</th>
                        <th class="pb-3">Registration Date</th>
                        <th class="pb-3">Assigned Classes</th>
                        <th class="pb-3">Action</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <!-- Sophia Martinez -->
                    <tr class="hover:bg-gray-50">
                        <td class="py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-blue-600 font-medium text-sm">SM</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Sophia Martinez</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4">
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded font-medium">Intermediate</span>
                        </td>
                        <td class="py-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded font-medium">Active</span>
                        </td>
                        <td class="py-4 text-sm text-gray-600">Dec 15, 2023</td>
                        <td class="py-4 text-sm text-gray-600">2 classes</td>
                        <td class="py-4">
                            <a href="#" class="text-blue-600 text-sm font-medium hover:text-blue-700">View Profile</a>
                        </td>
                    </tr>

                    <!-- James Wilson -->
                    <tr class="hover:bg-gray-50">
                        <td class="py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <span class="text-yellow-600 font-medium text-sm">JW</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">James Wilson</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4">
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded font-medium">Beginner</span>
                        </td>
                        <td class="py-4">
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded font-medium">Pending</span>
                        </td>
                        <td class="py-4 text-sm text-gray-600">Dec 14, 2023</td>
                        <td class="py-4 text-sm text-gray-600">1 class</td>
                        <td class="py-4">
                            <a href="#" class="text-blue-600 text-sm font-medium hover:text-blue-700">View Profile</a>
                        </td>
                    </tr>

                    <!-- Olivia Kim -->
                    <tr class="hover:bg-gray-50">
                        <td class="py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span class="text-purple-600 font-medium text-sm">OK</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Olivia Kim</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4">
                            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded font-medium">Advanced</span>
                        </td>
                        <td class="py-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded font-medium">Active</span>
                        </td>
                        <td class="py-4 text-sm text-gray-600">Dec 12, 2023</td>
                        <td class="py-4 text-sm text-gray-600">3 classes</td>
                        <td class="py-4">
                            <a href="#" class="text-blue-600 text-sm font-medium hover:text-blue-700">View Profile</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Student Distribution Pie Chart
const studentCtx = document.getElementById('studentDistributionChart').getContext('2d');
const studentChart = new Chart(studentCtx, {
    type: 'doughnut',
    data: {
        labels: ['Beginner', 'Intermediate', 'Upper-Intermediate', 'Advanced'],
        datasets: [{
            data: [{{ beginner_count|default:45 }}, {{ intermediate_count|default:85 }}, {{ upper_intermediate_count|default:65 }}, {{ advanced_count|default:53 }}],
            backgroundColor: [
                '#FBBF24', // Yellow
                '#3B82F6', // Blue
                '#8B5CF6', // Purple
                '#10B981'  // Green
            ],
            borderWidth: 0,
            cutout: '60%'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Revenue Line Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
            label: 'Revenue',
            data: [8000, 9500, 10200, 11000, 11800, 12450],
            borderColor: '#10B981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#10B981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: '#f3f4f6'
                },
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});
</script>
{% endblock %}
