{% extends 'center/base.html' %}

{% block title %}Dashboard - English Language Center{% endblock %}
{% block page_title %}Dashboard Overview{% endblock %}

{% block extra_css %}
<style>
/* Dashboard Styles */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh;
}

.dashboard-container {
    background: transparent;
    min-height: 100vh;
    padding: 2rem 0;
}

.stat-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.2);
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;
    background: linear-gradient(90deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.6) 100%);
}

.chart-container {
    position: relative;
    height: 350px;
    width: 100%;
}

.metric-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.metric-up {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.metric-down {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.section-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.section-card:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.export-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.export-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.export-card:hover::before {
    left: 100%;
}

.quick-action-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="container mx-auto px-4">

        <!-- Welcome Section -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">Welcome to Language Center</h1>
            <p class="text-white/80 text-lg">Manage your students, teachers, and classes efficiently</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Students Card -->
            <div class="stat-card section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg mr-3">
                                <i class="fas fa-user-graduate text-white text-lg"></i>
                            </div>
                            <p class="text-gray-600 text-sm font-semibold">Total Students</p>
                        </div>
                        <h3 class="text-4xl font-bold text-gray-800 mb-3">{{ total_students|default:0 }}</h3>
                        <div class="flex items-center">
                            <span class="metric-badge metric-up">
                                <i class="fas fa-arrow-up mr-1"></i> 12%
                            </span>
                            <span class="text-gray-500 text-sm ml-2">from last month</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span class="font-medium">Active Students</span>
                        <span class="font-bold">{{ active_students|default:total_students|default:0 }}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {% if total_students %}{% widthratio active_students|default:total_students total_students 100 %}{% else %}0{% endif %}%"></div>
                    </div>
                </div>
            </div>

            <!-- Teachers Card -->
            <div class="stat-card section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <div class="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg mr-3">
                                <i class="fas fa-chalkboard-teacher text-white text-lg"></i>
                            </div>
                            <p class="text-gray-600 text-sm font-semibold">Total Teachers</p>
                        </div>
                        <h3 class="text-4xl font-bold text-gray-800 mb-3">{{ total_teachers|default:0 }}</h3>
                        <div class="flex items-center">
                            <span class="metric-badge metric-up">
                                <i class="fas fa-arrow-up mr-1"></i> 5%
                            </span>
                            <span class="text-gray-500 text-sm ml-2">from last month</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span class="font-medium">Active Teachers</span>
                        <span class="font-bold">{{ active_teachers|default:total_teachers|default:0 }}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {% if total_teachers %}{% widthratio active_teachers|default:total_teachers total_teachers 100 %}{% else %}0{% endif %}%"></div>
                    </div>
                </div>
            </div>

            <!-- Classes Card -->
            <div class="stat-card section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-2 rounded-lg mr-3">
                                <i class="fas fa-users text-white text-lg"></i>
                            </div>
                            <p class="text-gray-600 text-sm font-semibold">Active Classes</p>
                        </div>
                        <h3 class="text-4xl font-bold text-gray-800 mb-3">{{ total_classes|default:0 }}</h3>
                        <div class="flex items-center">
                            <span class="metric-badge metric-up">
                                <i class="fas fa-arrow-up mr-1"></i> 8%
                            </span>
                            <span class="text-gray-500 text-sm ml-2">from last month</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span class="font-medium">Avg. Capacity</span>
                        <span class="font-bold">82%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 82%"></div>
                    </div>
                </div>
            </div>

            <!-- Revenue Card -->
            <div class="stat-card section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <div class="bg-gradient-to-r from-yellow-500 to-orange-500 p-2 rounded-lg mr-3">
                                <i class="fas fa-dollar-sign text-white text-lg"></i>
                            </div>
                            <p class="text-gray-600 text-sm font-semibold">Monthly Revenue</p>
                        </div>
                        <h3 class="text-4xl font-bold text-gray-800 mb-3">${{ monthly_revenue|floatformat:0|default:"12,450" }}</h3>
                        <div class="flex items-center">
                            <span class="metric-badge metric-up">
                                <i class="fas fa-arrow-up mr-1"></i> 15%
                            </span>
                            <span class="text-gray-500 text-sm ml-2">from last month</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span class="font-medium">Target Progress</span>
                        <span class="font-bold">$15,000</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {% widthratio monthly_revenue|default:12450 15000 100 %}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Student Distribution Chart -->
            <div class="section-card rounded-2xl shadow-xl p-8">
                <div class="flex justify-between items-center mb-8">
                    <h3 class="text-xl font-bold text-gray-800">Student Distribution by Level</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">Monthly</button>
                        <button class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-colors">Yearly</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="studentDistributionChart"></canvas>
                </div>
                <div class="grid grid-cols-2 gap-4 mt-8">
                    <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                        <div class="w-4 h-4 bg-yellow-400 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-700">Beginner</span>
                    </div>
                    <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-700">Intermediate</span>
                    </div>
                    <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                        <div class="w-4 h-4 bg-purple-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-700">Upper-Intermediate</span>
                    </div>
                    <div class="flex items-center p-3 bg-green-50 rounded-lg">
                        <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-700">Advanced</span>
                    </div>
                </div>
            </div>

            <!-- Revenue Chart -->
            <div class="section-card rounded-2xl shadow-xl p-8">
                <div class="flex justify-between items-center mb-8">
                    <h3 class="text-xl font-bold text-gray-800">Revenue Overview</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">Monthly</button>
                        <button class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-colors">Yearly</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="section-card rounded-2xl shadow-xl p-8 mb-8">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-2">Quick Actions</h3>
                <p class="text-gray-600">Manage your language center efficiently</p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Add Student -->
                <a href="{% url 'center:student_create' %}" class="quick-action-btn flex flex-col items-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl hover:from-blue-100 hover:to-blue-200 transition-all duration-300 group">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-user-plus text-white text-2xl"></i>
                    </div>
                    <span class="text-lg font-semibold text-gray-800">Add Student</span>
                    <span class="text-sm text-gray-600 mt-1">Register new student</span>
                </a>

                <!-- Add Teacher -->
                <a href="{% url 'center:teacher_create' %}" class="quick-action-btn flex flex-col items-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl hover:from-green-100 hover:to-green-200 transition-all duration-300 group">
                    <div class="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chalkboard-teacher text-white text-2xl"></i>
                    </div>
                    <span class="text-lg font-semibold text-gray-800">Add Teacher</span>
                    <span class="text-sm text-gray-600 mt-1">Hire new teacher</span>
                </a>

                <!-- Create Class -->
                <a href="{% url 'center:class_create' %}" class="quick-action-btn flex flex-col items-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl hover:from-purple-100 hover:to-purple-200 transition-all duration-300 group">
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-plus text-white text-2xl"></i>
                    </div>
                    <span class="text-lg font-semibold text-gray-800">Create Class</span>
                    <span class="text-sm text-gray-600 mt-1">Setup new class</span>
                </a>

                <!-- Record Payment -->
                <a href="{% url 'center:payment_create' %}" class="quick-action-btn flex flex-col items-center p-6 bg-gradient-to-br from-yellow-50 to-orange-100 rounded-2xl hover:from-yellow-100 hover:to-orange-200 transition-all duration-300 group">
                    <div class="bg-gradient-to-r from-yellow-500 to-orange-500 p-4 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-dollar-sign text-white text-2xl"></i>
                    </div>
                    <span class="text-lg font-semibold text-gray-800">Record Payment</span>
                    <span class="text-sm text-gray-600 mt-1">Process payment</span>
                </a>
            </div>
        </div>

        <!-- Additional Dashboard Sections -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Upcoming Classes -->
            <div class="section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">Upcoming Classes</h3>
                    <a href="{% url 'center:class_list' %}" class="text-blue-600 text-sm font-medium hover:text-blue-700 bg-blue-50 px-3 py-1 rounded-lg transition-colors">View All</a>
                </div>
        <div class="space-y-4">
            <!-- Intermediate Conversation -->
            <div class="flex items-start space-x-3">
                <div class="bg-blue-100 p-2 rounded-lg">
                    <i class="fas fa-users text-blue-600 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">Intermediate Conversation</h4>
                    <p class="text-sm text-gray-500">Mon, Wed, Fri • 10:00 AM - 11:30 AM</p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Room 201</span>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Sarah Johnson</span>
                    </div>
                </div>
            </div>

            <!-- Advanced Grammar -->
            <div class="flex items-start space-x-3">
                <div class="bg-purple-100 p-2 rounded-lg">
                    <i class="fas fa-users text-purple-600 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">Advanced Grammar</h4>
                    <p class="text-sm text-gray-500">Tue, Thu • 2:00 PM - 3:30 PM</p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Room 105</span>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Michael Chen</span>
                    </div>
                </div>
            </div>

            <!-- Beginner Writing -->
            <div class="flex items-start space-x-3">
                <div class="bg-yellow-100 p-2 rounded-lg">
                    <i class="fas fa-users text-yellow-600 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">Beginner Writing</h4>
                    <p class="text-sm text-gray-500">Sat • 9:00 AM - 12:00 PM</p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Room 302</span>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Emma Rodriguez</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

            <!-- Recent Payments -->
            <div class="section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">Recent Payments</h3>
                    <a href="{% url 'center:payment_list' %}" class="text-blue-600 text-sm font-medium hover:text-blue-700 bg-blue-50 px-3 py-1 rounded-lg transition-colors">View All</a>
                </div>
        <div class="space-y-4">
            <!-- Sarah Johnson Payment -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Sarah Johnson</h4>
                        <p class="text-sm text-gray-500">Intermediate Conversation</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$150.00</p>
                    <p class="text-xs text-gray-500">Today, 10:30 AM</p>
                </div>
            </div>

            <!-- David Wilson Payment -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">David Wilson</h4>
                        <p class="text-sm text-gray-500">Advanced Listening</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$180.00</p>
                    <p class="text-xs text-gray-500">Yesterday, 3:15 PM</p>
                </div>
            </div>

            <!-- Emma Rodriguez Payment -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Emma Rodriguez</h4>
                        <p class="text-sm text-gray-500">Beginner Writing</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$120.00</p>
                    <p class="text-xs text-gray-500">Dec 12, 2:45 PM</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-900 mb-6">Quick Actions</h3>
        <div class="grid grid-cols-2 gap-4">
            <!-- Add Student -->
            <a href="{% url 'center:student_create' %}" class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                <div class="bg-blue-100 p-3 rounded-full mb-2">
                    <i class="fas fa-user-plus text-blue-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Add Student</span>
            </a>

            <!-- Add Teacher -->
            <a href="{% url 'center:teacher_create' %}" class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                <div class="bg-green-100 p-3 rounded-full mb-2">
                    <i class="fas fa-chalkboard-teacher text-green-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Add Teacher</span>
            </a>

            <!-- Create Class -->
            <a href="{% url 'center:class_create' %}" class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                <div class="bg-purple-100 p-3 rounded-full mb-2">
                    <i class="fas fa-plus text-purple-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Create Class</span>
            </a>

            <!-- Record Payment -->
            <a href="{% url 'center:payment_create' %}" class="flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                <div class="bg-yellow-100 p-3 rounded-full mb-2">
                    <i class="fas fa-dollar-sign text-yellow-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Record Payment</span>
            </a>
        </div>
    </div>
</div>

        <!-- Export Section -->
        <div class="section-card rounded-2xl shadow-xl p-8 mb-8">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-2">Export Data</h3>
                <p class="text-gray-600">Download your data in professional Excel format</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Export All Data -->
                <a href="{% url 'center:export_dashboard' %}" class="export-card flex flex-col items-center p-6 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-2xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    <div class="bg-white bg-opacity-20 p-4 rounded-2xl mb-4">
                        <i class="fas fa-download text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-lg mb-2">Complete Report</h4>
                    <p class="text-sm opacity-90 text-center">All data in one comprehensive Excel file</p>
                </a>

                <!-- Export Students -->
                <a href="{% url 'center:export_students' %}" class="export-card flex flex-col items-center p-6 bg-gradient-to-br from-green-500 to-green-600 text-white rounded-2xl hover:from-green-600 hover:to-green-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    <div class="bg-white bg-opacity-20 p-4 rounded-2xl mb-4">
                        <i class="fas fa-user-graduate text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-lg mb-2">Students Data</h4>
                    <p class="text-sm opacity-90 text-center">Complete student information and records</p>
                </a>

                <!-- Export Teachers -->
                <a href="{% url 'center:export_teachers' %}" class="export-card flex flex-col items-center p-6 bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-2xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    <div class="bg-white bg-opacity-20 p-4 rounded-2xl mb-4">
                        <i class="fas fa-chalkboard-teacher text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-lg mb-2">Teachers Data</h4>
                    <p class="text-sm opacity-90 text-center">Teacher profiles and class assignments</p>
                </a>

                <!-- Export Payments -->
                <a href="{% url 'center:export_payments' %}" class="export-card flex flex-col items-center p-6 bg-gradient-to-br from-yellow-500 to-orange-500 text-white rounded-2xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    <div class="bg-white bg-opacity-20 p-4 rounded-2xl mb-4">
                        <i class="fas fa-receipt text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-lg mb-2">Payments Data</h4>
                    <p class="text-sm opacity-90 text-center">Financial records and transactions</p>
                </a>
    </div>


            <div class="mt-8 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
                <div class="flex items-center text-gray-700">
                    <div class="bg-blue-100 p-2 rounded-full mr-4">
                        <i class="fas fa-info-circle text-blue-600"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-1">Professional Excel Reports</h4>
                        <p class="text-sm">Excel files include formatted data with headers, borders, and professional styling. Complete report contains all data in separate sheets.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <!-- System Status and Recent Student Registrations -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- System Status -->
            <div class="section-card rounded-2xl shadow-xl p-8">
                <h3 class="text-xl font-bold text-gray-800 mb-6">System Status</h3>
                <div class="space-y-6">
                    <!-- Database Status -->
                    <div class="flex justify-between items-center p-4 bg-green-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="bg-green-100 p-2 rounded-lg mr-3">
                                <i class="fas fa-database text-green-600"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Database</span>
                        </div>
                        <span class="bg-green-100 text-green-800 text-sm px-4 py-2 rounded-full font-semibold">Operational</span>
                    </div>

                    <!-- Server Uptime -->
                    <div class="flex justify-between items-center p-4 bg-blue-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                <i class="fas fa-server text-blue-600"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Server Uptime</span>
                        </div>
                        <span class="text-gray-800 font-bold">99.8%</span>
                    </div>

                    <!-- Storage -->
                    <div class="flex justify-between items-center p-4 bg-yellow-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                                <i class="fas fa-hdd text-yellow-600"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Storage</span>
                        </div>
                        <span class="text-gray-800 font-bold">42% used</span>
                    </div>

                    <!-- Last Backup -->
                    <div class="flex justify-between items-center p-4 bg-purple-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                <i class="fas fa-cloud-upload-alt text-purple-600"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Last Backup</span>
                        </div>
                        <span class="text-gray-800 font-bold">Dec 15, 2023</span>
                    </div>
                </div>
            </div>

            <!-- Recent Student Registrations -->
            <div class="section-card rounded-2xl shadow-xl p-8">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">Recent Student Registrations</h3>
                    <a href="{% url 'center:student_list' %}" class="text-blue-600 text-sm font-medium hover:text-blue-700 bg-blue-50 px-3 py-1 rounded-lg transition-colors">View All Students</a>
                </div>

                <!-- Table Header -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">
                                <th class="pb-4 font-semibold">Student</th>
                                <th class="pb-4 font-semibold">Level</th>
                                <th class="pb-4 font-semibold">Status</th>
                                <th class="pb-4 font-semibold">Registration Date</th>
                                <th class="pb-4 font-semibold">Assigned Classes</th>
                                <th class="pb-4 font-semibold">Action</th>
                            </tr>
                        </thead>
                <tbody class="divide-y divide-gray-200">
                    <!-- Sophia Martinez -->
                    <tr class="hover:bg-gray-50">
                        <td class="py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-blue-600 font-medium text-sm">SM</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Sophia Martinez</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4">
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded font-medium">Intermediate</span>
                        </td>
                        <td class="py-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded font-medium">Active</span>
                        </td>
                        <td class="py-4 text-sm text-gray-600">Dec 15, 2023</td>
                        <td class="py-4 text-sm text-gray-600">2 classes</td>
                        <td class="py-4">
                            <a href="#" class="text-blue-600 text-sm font-medium hover:text-blue-700">View Profile</a>
                        </td>
                    </tr>

                    <!-- James Wilson -->
                    <tr class="hover:bg-gray-50">
                        <td class="py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <span class="text-yellow-600 font-medium text-sm">JW</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">James Wilson</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4">
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded font-medium">Beginner</span>
                        </td>
                        <td class="py-4">
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded font-medium">Pending</span>
                        </td>
                        <td class="py-4 text-sm text-gray-600">Dec 14, 2023</td>
                        <td class="py-4 text-sm text-gray-600">1 class</td>
                        <td class="py-4">
                            <a href="#" class="text-blue-600 text-sm font-medium hover:text-blue-700">View Profile</a>
                        </td>
                    </tr>

                    <!-- Olivia Kim -->
                    <tr class="hover:bg-gray-50">
                        <td class="py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span class="text-purple-600 font-medium text-sm">OK</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Olivia Kim</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4">
                            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded font-medium">Advanced</span>
                        </td>
                        <td class="py-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded font-medium">Active</span>
                        </td>
                        <td class="py-4 text-sm text-gray-600">Dec 12, 2023</td>
                        <td class="py-4 text-sm text-gray-600">3 classes</td>
                        <td class="py-4">
                            <a href="#" class="text-blue-600 text-sm font-medium hover:text-blue-700">View Profile</a>
                        </td>
                    </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Student Distribution Pie Chart
const studentCtx = document.getElementById('studentDistributionChart').getContext('2d');
const studentChart = new Chart(studentCtx, {
    type: 'doughnut',
    data: {
        labels: ['Beginner', 'Intermediate', 'Upper-Intermediate', 'Advanced'],
        datasets: [{
            data: [{{ beginner_count|default:45 }}, {{ intermediate_count|default:85 }}, {{ upper_intermediate_count|default:65 }}, {{ advanced_count|default:53 }}],
            backgroundColor: [
                '#FBBF24', // Yellow
                '#3B82F6', // Blue
                '#8B5CF6', // Purple
                '#10B981'  // Green
            ],
            borderWidth: 0,
            cutout: '60%'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Revenue Line Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
            label: 'Revenue',
            data: [8000, 9500, 10200, 11000, 11800, 12450],
            borderColor: '#10B981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#10B981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: '#f3f4f6'
                },
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});
</script>
{% endblock %}
