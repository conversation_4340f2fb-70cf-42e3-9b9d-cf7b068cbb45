{% extends 'center/base.html' %}

{% block title %}Dashboard - English Language Center{% endblock %}
{% block page_title %}Dashboard Overview{% endblock %}

{% block extra_css %}
<style>
/* Dashboard Styles */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh;
}

.dashboard-container {
    background: transparent;
    min-height: 100vh;
    padding: 2rem 0;
}

.stat-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.2);
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.8s ease;
    background: linear-gradient(90deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.6) 100%);
}

.chart-container {
    position: relative;
    height: 350px;
    width: 100%;
}

.metric-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.metric-up {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.metric-down {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.section-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.section-card:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.export-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.export-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.export-card:hover::before {
    left: 100%;
}

.quick-action-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Notification Center Styles */
.notification-center {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.notification-center::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
    animation: notificationGlow 3s ease-in-out infinite alternate;
}

@keyframes notificationGlow {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
}

.notification-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 4px 12px;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.notification-section {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 1.5rem;
}

.notification-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.notification-item {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.notification-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.notification-item:hover::before {
    left: 100%;
}

.critical-alert {
    border-left-width: 6px !important;
    animation: criticalPulse 2s infinite;
}

@keyframes criticalPulse {
    0%, 100% { border-left-color: #ef4444; }
    50% { border-left-color: #dc2626; }
}

.warning-alert {
    border-left-width: 4px !important;
}

.failed-alert {
    border-left-width: 4px !important;
    position: relative;
}

.failed-alert::after {
    content: '⚠️';
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 1.2rem;
    animation: shake 1s infinite;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

.ready-alert {
    border-left-width: 4px !important;
    position: relative;
}

.ready-alert::after {
    content: '✉️';
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 1.2rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.send-reminder-btn, .retry-email-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.send-reminder-btn:hover, .retry-email-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.send-reminder-btn::before, .retry-email-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.3s;
}

.send-reminder-btn:hover::before, .retry-email-btn:hover::before {
    left: 100%;
}

.notification-loading {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.notification-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.notification-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 10px 0;
    animation: slideInFromTop 0.5s ease-out;
}

.notification-error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    margin: 10px 0;
    animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.notification-count {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.urgency-indicator {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: urgencyPulse 1.5s infinite;
}

.urgency-critical {
    background: #ef4444;
}

.urgency-high {
    background: #f97316;
}

.urgency-medium {
    background: #eab308;
}

.urgency-low {
    background: #3b82f6;
}

@keyframes urgencyPulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="container mx-auto px-4">

        <!-- Welcome Section -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-black mb-2">Welcome to Language Center</h1>
            <p class="text-black/80 text-lg">Manage your students, teachers, and classes efficiently</p>
        </div>

        <!-- Notification Center -->
        {% if total_notifications > 0 %}
        <div id="notifications" class="notification-center section-card rounded-2xl shadow-xl p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-bell text-yellow-500 mr-3"></i>
                    Payment Notifications
                    <span class="notification-badge ml-3">{{ total_notifications }}</span>
                </h3>
                <button id="dismissAllBtn" class="text-sm text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-times mr-1"></i>Dismiss All
                </button>
            </div>

            <!-- Critical Overdue Payments -->
            {% if overdue_payments_list %}
            <div class="notification-section mb-6">
                <h4 class="text-lg font-semibold text-red-600 mb-4 flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Overdue Payments ({{ overdue_payments_list|length }})
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for item in overdue_payments_list|slice:":6" %}
                    <div class="notification-item critical-alert p-4 rounded-lg border-l-4 border-red-500 bg-red-50">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <h5 class="font-semibold text-gray-900">{{ item.student.full_name }}</h5>
                                <p class="text-sm text-gray-600">{{ item.student.email }}</p>
                                <p class="text-lg font-bold text-red-600">${{ item.payment.amount }}</p>
                                <p class="text-sm text-red-700">
                                    <i class="fas fa-clock mr-1"></i>
                                    {{ item.days_overdue }} day{{ item.days_overdue|pluralize }} overdue
                                </p>
                            </div>
                            <div class="flex flex-col space-y-2">
                                <button class="send-reminder-btn bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 transition-colors"
                                        data-student-id="{{ item.student.id }}" data-payment-id="{{ item.payment.id }}">
                                    <i class="fas fa-envelope mr-1"></i>Send Urgent
                                </button>
                                <a href="{% url 'center:student_detail' item.student.id %}"
                                   class="bg-gray-500 text-white px-3 py-1 rounded text-xs hover:bg-gray-600 transition-colors text-center">
                                    <i class="fas fa-user mr-1"></i>View
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if overdue_payments_list|length > 6 %}
                <p class="text-sm text-gray-600 mt-2">
                    <i class="fas fa-info-circle mr-1"></i>
                    Showing 6 of {{ overdue_payments_list|length }} overdue payments
                </p>
                {% endif %}
            </div>
            {% endif %}

            <!-- Upcoming Due Dates -->
            {% if upcoming_due_dates %}
            <div class="notification-section mb-6">
                <h4 class="text-lg font-semibold text-yellow-600 mb-4 flex items-center">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Upcoming Due Dates ({{ upcoming_due_dates|length }})
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {% for item in upcoming_due_dates|slice:":9" %}
                    <div class="notification-item warning-alert p-4 rounded-lg border-l-4
                                {% if item.urgency == 'high' %}border-orange-500 bg-orange-50
                                {% elif item.urgency == 'medium' %}border-yellow-500 bg-yellow-50
                                {% else %}border-blue-500 bg-blue-50{% endif %}">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <h5 class="font-semibold text-gray-900">{{ item.student.full_name }}</h5>
                                <p class="text-sm text-gray-600">{{ item.student.email }}</p>
                                <p class="text-lg font-bold
                                          {% if item.urgency == 'high' %}text-orange-600
                                          {% elif item.urgency == 'medium' %}text-yellow-600
                                          {% else %}text-blue-600{% endif %}">${{ item.payment.amount }}</p>
                                <p class="text-sm
                                          {% if item.urgency == 'high' %}text-orange-700
                                          {% elif item.urgency == 'medium' %}text-yellow-700
                                          {% else %}text-blue-700{% endif %}">
                                    <i class="fas fa-clock mr-1"></i>
                                    Due in {{ item.days_until_due }} day{{ item.days_until_due|pluralize }}
                                </p>
                            </div>
                            <div class="flex flex-col space-y-1">
                                <button class="send-reminder-btn
                                              {% if item.urgency == 'high' %}bg-orange-600 hover:bg-orange-700
                                              {% elif item.urgency == 'medium' %}bg-yellow-600 hover:bg-yellow-700
                                              {% else %}bg-blue-600 hover:bg-blue-700{% endif %}
                                              text-white px-2 py-1 rounded text-xs transition-colors"
                                        data-student-id="{{ item.student.id }}" data-payment-id="{{ item.payment.id }}">
                                    <i class="fas fa-envelope mr-1"></i>Remind
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Failed Email Notifications -->
            {% if failed_notifications %}
            <div class="notification-section mb-6">
                <h4 class="text-lg font-semibold text-purple-600 mb-4 flex items-center">
                    <i class="fas fa-envelope-open-text mr-2"></i>
                    Failed Email Notifications ({{ failed_notifications|length }})
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% for notification in failed_notifications|slice:":4" %}
                    <div class="notification-item failed-alert p-4 rounded-lg border-l-4 border-purple-500 bg-purple-50">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <h5 class="font-semibold text-gray-900">{{ notification.student.full_name }}</h5>
                                <p class="text-sm text-gray-600">{{ notification.student.email }}</p>
                                <p class="text-sm text-purple-700">{{ notification.get_notification_type_display }}</p>
                                <p class="text-xs text-red-600 mt-1">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    {{ notification.error_message|truncatechars:50 }}
                                </p>
                            </div>
                            <div class="flex flex-col space-y-1">
                                <button class="retry-email-btn bg-purple-600 text-white px-2 py-1 rounded text-xs hover:bg-purple-700 transition-colors"
                                        data-notification-id="{{ notification.id }}">
                                    <i class="fas fa-redo mr-1"></i>Retry
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Students Needing Reminders -->
            {% if students_needing_reminders %}
            <div class="notification-section">
                <h4 class="text-lg font-semibold text-green-600 mb-4 flex items-center">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Ready to Send Reminders ({{ students_needing_reminders|length }})
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {% for item in students_needing_reminders|slice:":6" %}
                    <div class="notification-item ready-alert p-4 rounded-lg border-l-4 border-green-500 bg-green-50">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <h5 class="font-semibold text-gray-900">{{ item.student.full_name }}</h5>
                                <p class="text-sm text-gray-600">{{ item.student.email }}</p>
                                <p class="text-lg font-bold text-green-600">${{ item.payment.amount }}</p>
                                <p class="text-sm text-green-700">
                                    <i class="fas fa-clock mr-1"></i>
                                    Due in {{ item.days_until_due }} day{{ item.days_until_due|pluralize }}
                                </p>
                            </div>
                            <div class="flex flex-col space-y-1">
                                <button class="send-reminder-btn bg-green-600 text-white px-2 py-1 rounded text-xs hover:bg-green-700 transition-colors"
                                        data-student-id="{{ item.student.id }}" data-payment-id="{{ item.payment.id }}">
                                    <i class="fas fa-envelope mr-1"></i>Send
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% if students_needing_reminders|length > 6 %}
                <div class="mt-4 text-center">
                    <button id="sendAllRemindersBtn" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send All {{ students_needing_reminders|length }} Reminders
                    </button>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Students Card -->
            <div class="stat-card section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg mr-3">
                                <i class="fas fa-user-graduate text-white text-lg"></i>
                            </div>
                            <p class="text-gray-600 text-sm font-semibold">Total Students</p>
                        </div>
                        <h3 class="text-4xl font-bold text-gray-800 mb-3">{{ total_students|default:0 }}</h3>
                        <div class="flex items-center">
                            <span class="metric-badge metric-up">
                                <i class="fas fa-arrow-up mr-1"></i> 12%
                            </span>
                            <span class="text-gray-500 text-sm ml-2">from last month</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span class="font-medium">Active Students</span>
                        <span class="font-bold">{{ active_students|default:total_students|default:0 }}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {% if total_students %}{% widthratio active_students|default:total_students total_students 100 %}{% else %}0{% endif %}%"></div>
                    </div>
                </div>
            </div>

            <!-- Teachers Card -->
            <div class="stat-card section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <div class="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-lg mr-3">
                                <i class="fas fa-chalkboard-teacher text-white text-lg"></i>
                            </div>
                            <p class="text-gray-600 text-sm font-semibold">Total Teachers</p>
                        </div>
                        <h3 class="text-4xl font-bold text-gray-800 mb-3">{{ total_teachers|default:0 }}</h3>
                        <div class="flex items-center">
                            <span class="metric-badge metric-up">
                                <i class="fas fa-arrow-up mr-1"></i> 5%
                            </span>
                            <span class="text-gray-500 text-sm ml-2">from last month</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span class="font-medium">Active Teachers</span>
                        <span class="font-bold">{{ active_teachers|default:total_teachers|default:0 }}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {% if total_teachers %}{% widthratio active_teachers|default:total_teachers total_teachers 100 %}{% else %}0{% endif %}%"></div>
                    </div>
                </div>
            </div>

            <!-- Classes Card -->
            <div class="stat-card section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-2 rounded-lg mr-3">
                                <i class="fas fa-users text-white text-lg"></i>
                            </div>
                            <p class="text-gray-600 text-sm font-semibold">Active Classes</p>
                        </div>
                        <h3 class="text-4xl font-bold text-gray-800 mb-3">{{ total_classes|default:0 }}</h3>
                        <div class="flex items-center">
                            <span class="metric-badge metric-up">
                                <i class="fas fa-arrow-up mr-1"></i> 8%
                            </span>
                            <span class="text-gray-500 text-sm ml-2">from last month</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span class="font-medium">Avg. Capacity</span>
                        <span class="font-bold">82%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 82%"></div>
                    </div>
                </div>
            </div>

            <!-- Revenue Card -->
            <div class="stat-card section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-start">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <div class="bg-gradient-to-r from-yellow-500 to-orange-500 p-2 rounded-lg mr-3">
                                <i class="fas fa-dollar-sign text-white text-lg"></i>
                            </div>
                            <p class="text-gray-600 text-sm font-semibold">Monthly Revenue</p>
                        </div>
                        <h3 class="text-4xl font-bold text-gray-800 mb-3">${{ monthly_revenue|floatformat:0|default:"12,450" }}</h3>
                        <div class="flex items-center">
                            <span class="metric-badge metric-up">
                                <i class="fas fa-arrow-up mr-1"></i> 15%
                            </span>
                            <span class="text-gray-500 text-sm ml-2">from last month</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-2">
                        <span class="font-medium">Target Progress</span>
                        <span class="font-bold">$15,000</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {% widthratio monthly_revenue|default:12450 15000 100 %}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Student Distribution Chart -->
            <div class="section-card rounded-2xl shadow-xl p-8">
                <div class="flex justify-between items-center mb-8">
                    <h3 class="text-xl font-bold text-gray-800">Student Distribution by Level</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">Monthly</button>
                        <button class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-colors">Yearly</button>
                    </div>
                </div>
                <div class="chart-container relative">
                    <canvas id="studentDistributionChart"></canvas>
                    <div id="studentChartLoader" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 rounded-lg">
                        <div class="text-center">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-2"></div>
                            <p class="text-gray-600">Loading chart...</p>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4 mt-8">
                    {% if beginner_count > 0 %}
                    <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                        <div class="w-4 h-4 bg-yellow-400 rounded-full mr-3"></div>
                        <div class="flex-1">
                            <span class="text-sm font-medium text-gray-700">Beginner</span>
                            <span class="text-xs text-gray-500 ml-2">({{ beginner_count }})</span>
                        </div>
                    </div>
                    {% endif %}

                    {% if elementary_count > 0 %}
                    <div class="flex items-center p-3 bg-orange-50 rounded-lg">
                        <div class="w-4 h-4 bg-orange-400 rounded-full mr-3"></div>
                        <div class="flex-1">
                            <span class="text-sm font-medium text-gray-700">Elementary</span>
                            <span class="text-xs text-gray-500 ml-2">({{ elementary_count }})</span>
                        </div>
                    </div>
                    {% endif %}

                    {% if intermediate_count > 0 %}
                    <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                        <div class="flex-1">
                            <span class="text-sm font-medium text-gray-700">Intermediate</span>
                            <span class="text-xs text-gray-500 ml-2">({{ intermediate_count }})</span>
                        </div>
                    </div>
                    {% endif %}

                    {% if upper_intermediate_count > 0 %}
                    <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                        <div class="w-4 h-4 bg-purple-500 rounded-full mr-3"></div>
                        <div class="flex-1">
                            <span class="text-sm font-medium text-gray-700">Upper-Intermediate</span>
                            <span class="text-xs text-gray-500 ml-2">({{ upper_intermediate_count }})</span>
                        </div>
                    </div>
                    {% endif %}

                    {% if advanced_count > 0 %}
                    <div class="flex items-center p-3 bg-green-50 rounded-lg">
                        <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                        <div class="flex-1">
                            <span class="text-sm font-medium text-gray-700">Advanced</span>
                            <span class="text-xs text-gray-500 ml-2">({{ advanced_count }})</span>
                        </div>
                    </div>
                    {% endif %}

                    {% if proficiency_count > 0 %}
                    <div class="flex items-center p-3 bg-red-50 rounded-lg">
                        <div class="w-4 h-4 bg-red-500 rounded-full mr-3"></div>
                        <div class="flex-1">
                            <span class="text-sm font-medium text-gray-700">Proficiency</span>
                            <span class="text-xs text-gray-500 ml-2">({{ proficiency_count }})</span>
                        </div>
                    </div>
                    {% endif %}

                    {% if total_students == 0 %}
                    <div class="col-span-2 text-center p-6 bg-gray-50 rounded-lg">
                        <i class="fas fa-user-graduate text-gray-400 text-3xl mb-2"></i>
                        <p class="text-gray-500">No students registered yet</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Revenue Chart -->
            <div class="section-card rounded-2xl shadow-xl p-8">
                <div class="flex justify-between items-center mb-8">
                    <h3 class="text-xl font-bold text-gray-800">Revenue Overview</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">Monthly</button>
                        <button class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-colors">Yearly</button>
                    </div>
                </div>
                <div class="chart-container relative">
                    <canvas id="revenueChart"></canvas>
                    <div id="revenueChartLoader" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 rounded-lg">
                        <div class="text-center">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-2"></div>
                            <p class="text-gray-600">Loading chart...</p>
                        </div>
                    </div>
                </div>
                <div class="mt-6 grid grid-cols-3 gap-4">
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">${{ monthly_revenue|floatformat:0|default:"0" }}</div>
                        <div class="text-sm text-gray-600">This Month</div>
                    </div>
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">${{ total_revenue|floatformat:0|default:"0" }}</div>
                        <div class="text-sm text-gray-600">Total Revenue</div>
                    </div>
                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">{{ revenue_data|length|default:"0" }}</div>
                        <div class="text-sm text-gray-600">Months Tracked</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="section-card rounded-2xl shadow-xl p-8 mb-8">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-2">Quick Actions</h3>
                <p class="text-gray-600">Manage your language center efficiently</p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Add Student -->
                <a href="{% url 'center:student_create' %}" class="quick-action-btn flex flex-col items-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl hover:from-blue-100 hover:to-blue-200 transition-all duration-300 group">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-user-plus text-white text-2xl"></i>
                    </div>
                    <span class="text-lg font-semibold text-gray-800">Add Student</span>
                    <span class="text-sm text-gray-600 mt-1">Register new student</span>
                </a>

                <!-- Add Teacher -->
                <a href="{% url 'center:teacher_create' %}" class="quick-action-btn flex flex-col items-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-2xl hover:from-green-100 hover:to-green-200 transition-all duration-300 group">
                    <div class="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chalkboard-teacher text-white text-2xl"></i>
                    </div>
                    <span class="text-lg font-semibold text-gray-800">Add Teacher</span>
                    <span class="text-sm text-gray-600 mt-1">Hire new teacher</span>
                </a>

                <!-- Create Class -->
                <a href="{% url 'center:class_create' %}" class="quick-action-btn flex flex-col items-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl hover:from-purple-100 hover:to-purple-200 transition-all duration-300 group">
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-plus text-white text-2xl"></i>
                    </div>
                    <span class="text-lg font-semibold text-gray-800">Create Class</span>
                    <span class="text-sm text-gray-600 mt-1">Setup new class</span>
                </a>

                <!-- Record Payment -->
                <a href="{% url 'center:payment_create' %}" class="quick-action-btn flex flex-col items-center p-6 bg-gradient-to-br from-yellow-50 to-orange-100 rounded-2xl hover:from-yellow-100 hover:to-orange-200 transition-all duration-300 group">
                    <div class="bg-gradient-to-r from-yellow-500 to-orange-500 p-4 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-dollar-sign text-white text-2xl"></i>
                    </div>
                    <span class="text-lg font-semibold text-gray-800">Record Payment</span>
                    <span class="text-sm text-gray-600 mt-1">Process payment</span>
                </a>
            </div>
        </div>

        <!-- Student Analytics Section -->
        <div class="section-card rounded-2xl shadow-xl p-8 mb-8">
            <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-8 space-y-4 lg:space-y-0">
                <h3 class="text-2xl font-bold text-gray-800">Student Analytics</h3>
                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                    <!-- Date Filter -->
                    <div class="flex items-center space-x-2 bg-white p-2 rounded-xl border border-gray-200">
                        <i class="fas fa-calendar text-gray-400"></i>
                        <input type="date" id="startDate" class="border-0 text-sm focus:outline-none" placeholder="Start Date">
                        <span class="text-gray-400">to</span>
                        <input type="date" id="endDate" class="border-0 text-sm focus:outline-none" placeholder="End Date">
                        <button id="applyDateFilter" class="bg-blue-500 text-white px-3 py-1 rounded-lg text-sm hover:bg-blue-600 transition-colors">
                            <i class="fas fa-filter"></i>
                        </button>
                        <button id="clearDateFilter" class="bg-gray-500 text-white px-3 py-1 rounded-lg text-sm hover:bg-gray-600 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- View Buttons -->
                    <div class="flex space-x-2">
                        <button id="allStudentsBtn" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-colors">All Students</button>
                        <button id="payingStudentsBtn" class="px-4 py-2 bg-gray-100 text-gray-600 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">Paying Students</button>
                        <button id="recentStudentsBtn" class="px-4 py-2 bg-gray-100 text-gray-600 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">Recent (30 days)</button>
                    </div>
                </div>
            </div>

            <!-- Filter Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl">
                    <div class="flex items-center">
                        <div class="bg-blue-500 p-3 rounded-lg mr-4">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-2xl font-bold text-blue-800">{{ total_students }}</h4>
                            <p class="text-blue-600 font-medium">Total Students</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl">
                    <div class="flex items-center">
                        <div class="bg-green-500 p-3 rounded-lg mr-4">
                            <i class="fas fa-credit-card text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-2xl font-bold text-green-800">{{ total_revenue|floatformat:0|default:"0" }}</h4>
                            <p class="text-green-600 font-medium">Total Revenue</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl">
                    <div class="flex items-center">
                        <div class="bg-purple-500 p-3 rounded-lg mr-4">
                            <i class="fas fa-calendar-plus text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-2xl font-bold text-purple-800">{{ recent_registrations|length }}</h4>
                            <p class="text-purple-600 font-medium">Recent Registrations</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Student Tables -->
            <div id="studentTablesContainer">
                <!-- All Students Table -->
                <div id="allStudentsTable" class="student-table">
                    <h4 class="text-lg font-bold text-gray-800 mb-4">All Students with Payment Status</h4>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">
                                    <th class="pb-4 font-semibold">Student</th>
                                    <th class="pb-4 font-semibold">Level</th>
                                    <th class="pb-4 font-semibold">Registration Date</th>
                                    <th class="pb-4 font-semibold">Email</th>
                                    <th class="pb-4 font-semibold">Phone</th>
                                    <th class="pb-4 font-semibold">Status</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                {% for student in students_with_payments %}
                                <tr class="hover:bg-gray-50 transition-colors">
                                    <td class="py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
                                                <span class="text-white font-bold text-sm">{{ student.full_name|slice:":2"|upper }}</span>
                                            </div>
                                            <div>
                                                <p class="font-semibold text-gray-900">{{ student.full_name }}</p>
                                                <p class="text-sm text-gray-500">{{ student.email }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4">
                                        <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full font-medium">{{ student.get_level_display }}</span>
                                    </td>
                                    <td class="py-4 text-sm text-gray-600">{{ student.registration_date|date:"M d, Y" }}</td>
                                    <td class="py-4 text-sm text-gray-600">{{ student.email }}</td>
                                    <td class="py-4 text-sm text-gray-600">{{ student.phone_number|default:"N/A" }}</td>
                                    <td class="py-4">
                                        {% if student.is_active %}
                                            <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-medium">
                                                <i class="fas fa-check mr-1"></i>Active
                                            </span>
                                        {% else %}
                                            <span class="bg-red-100 text-red-800 text-xs px-3 py-1 rounded-full font-medium">
                                                <i class="fas fa-times mr-1"></i>Inactive
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="py-8 text-center text-gray-500">
                                        <i class="fas fa-user-graduate text-4xl mb-2"></i>
                                        <p>No students found</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Paying Students Table -->
                <div id="payingStudentsTable" class="student-table hidden">
                    <h4 class="text-lg font-bold text-gray-800 mb-4">All Students (Alternative View)</h4>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">
                                    <th class="pb-4 font-semibold">Student</th>
                                    <th class="pb-4 font-semibold">Level</th>
                                    <th class="pb-4 font-semibold">Registration Date</th>
                                    <th class="pb-4 font-semibold">Gender</th>
                                    <th class="pb-4 font-semibold">Status</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                {% for student in paying_students %}
                                <tr class="hover:bg-gray-50 transition-colors">
                                    <td class="py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center">
                                                <span class="text-white font-bold text-sm">{{ student.full_name|slice:":2"|upper }}</span>
                                            </div>
                                            <div>
                                                <p class="font-semibold text-gray-900">{{ student.full_name }}</p>
                                                <p class="text-sm text-gray-500">{{ student.email }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4">
                                        <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-medium">{{ student.get_level_display }}</span>
                                    </td>
                                    <td class="py-4 text-sm text-gray-600">{{ student.registration_date|date:"M d, Y" }}</td>
                                    <td class="py-4 text-sm text-gray-600">{{ student.get_gender_display }}</td>
                                    <td class="py-4">
                                        <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-medium">
                                            <i class="fas fa-check mr-1"></i>Active
                                        </span>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="py-8 text-center text-gray-500">
                                        <i class="fas fa-user-graduate text-4xl mb-2"></i>
                                        <p>No students found</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Recent Students Table -->
                <div id="recentStudentsTable" class="student-table hidden">
                    <h4 class="text-lg font-bold text-gray-800 mb-4">Students Registered in Last 30 Days</h4>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">
                                    <th class="pb-4 font-semibold">Student</th>
                                    <th class="pb-4 font-semibold">Level</th>
                                    <th class="pb-4 font-semibold">Registration Date</th>
                                    <th class="pb-4 font-semibold">Days Since Registration</th>
                                    <th class="pb-4 font-semibold">Phone</th>
                                    <th class="pb-4 font-semibold">Status</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                {% for student in recent_registrations %}
                                <tr class="hover:bg-gray-50 transition-colors">
                                    <td class="py-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-10 h-10 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center">
                                                <span class="text-white font-bold text-sm">{{ student.full_name|slice:":2"|upper }}</span>
                                            </div>
                                            <div>
                                                <p class="font-semibold text-gray-900">{{ student.full_name }}</p>
                                                <p class="text-sm text-gray-500">{{ student.email }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="py-4">
                                        <span class="bg-purple-100 text-purple-800 text-xs px-3 py-1 rounded-full font-medium">{{ student.get_level_display }}</span>
                                    </td>
                                    <td class="py-4 text-sm text-gray-600">{{ student.registration_date|date:"M d, Y" }}</td>
                                    <td class="py-4">
                                        {% now "Y-m-d" as today %}
                                        {% with days_since=student.registration_date|timesince %}
                                            <span class="bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full font-medium">{{ days_since|slice:":10" }}</span>
                                        {% endwith %}
                                    </td>
                                    <td class="py-4 text-sm text-gray-600">{{ student.phone_number|default:"N/A" }}</td>
                                    <td class="py-4">
                                        <span class="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full font-medium">
                                            <i class="fas fa-check mr-1"></i>Active
                                        </span>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="py-8 text-center text-gray-500">
                                        <i class="fas fa-calendar-plus text-4xl mb-2"></i>
                                        <p>No recent registrations in the last 30 days</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Dashboard Sections -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Upcoming Classes -->
            <div class="section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">Upcoming Classes</h3>
                    <a href="{% url 'center:class_list' %}" class="text-blue-600 text-sm font-medium hover:text-blue-700 bg-blue-50 px-3 py-1 rounded-lg transition-colors">View All</a>
                </div>
        <div class="space-y-4">
            <!-- Intermediate Conversation -->
            <div class="flex items-start space-x-3">
                <div class="bg-blue-100 p-2 rounded-lg">
                    <i class="fas fa-users text-blue-600 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">Intermediate Conversation</h4>
                    <p class="text-sm text-gray-500">Mon, Wed, Fri • 10:00 AM - 11:30 AM</p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Room 201</span>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Sarah Johnson</span>
                    </div>
                </div>
            </div>

            <!-- Advanced Grammar -->
            <div class="flex items-start space-x-3">
                <div class="bg-purple-100 p-2 rounded-lg">
                    <i class="fas fa-users text-purple-600 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">Advanced Grammar</h4>
                    <p class="text-sm text-gray-500">Tue, Thu • 2:00 PM - 3:30 PM</p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Room 105</span>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Michael Chen</span>
                    </div>
                </div>
            </div>

            <!-- Beginner Writing -->
            <div class="flex items-start space-x-3">
                <div class="bg-yellow-100 p-2 rounded-lg">
                    <i class="fas fa-users text-yellow-600 text-sm"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">Beginner Writing</h4>
                    <p class="text-sm text-gray-500">Sat • 9:00 AM - 12:00 PM</p>
                    <div class="flex items-center space-x-2 mt-2">
                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">Room 302</span>
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">Emma Rodriguez</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

            <!-- Recent Payments -->
            <div class="section-card rounded-2xl shadow-xl p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">Recent Payments</h3>
                    <a href="{% url 'center:payment_list' %}" class="text-blue-600 text-sm font-medium hover:text-blue-700 bg-blue-50 px-3 py-1 rounded-lg transition-colors">View All</a>
                </div>
        <div class="space-y-4">
            <!-- Sarah Johnson Payment -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Sarah Johnson</h4>
                        <p class="text-sm text-gray-500">Intermediate Conversation</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$150.00</p>
                    <p class="text-xs text-gray-500">Today, 10:30 AM</p>
                </div>
            </div>

            <!-- David Wilson Payment -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">David Wilson</h4>
                        <p class="text-sm text-gray-500">Advanced Listening</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$180.00</p>
                    <p class="text-xs text-gray-500">Yesterday, 3:15 PM</p>
                </div>
            </div>

            <!-- Emma Rodriguez Payment -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="bg-green-100 p-2 rounded-full">
                        <i class="fas fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900">Emma Rodriguez</h4>
                        <p class="text-sm text-gray-500">Beginner Writing</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="font-bold text-green-600">$120.00</p>
                    <p class="text-xs text-gray-500">Dec 12, 2:45 PM</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-900 mb-6">Quick Actions</h3>
        <div class="grid grid-cols-2 gap-4">
            <!-- Add Student -->
            <a href="{% url 'center:student_create' %}" class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                <div class="bg-blue-100 p-3 rounded-full mb-2">
                    <i class="fas fa-user-plus text-blue-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Add Student</span>
            </a>

            <!-- Add Teacher -->
            <a href="{% url 'center:teacher_create' %}" class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                <div class="bg-green-100 p-3 rounded-full mb-2">
                    <i class="fas fa-chalkboard-teacher text-green-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Add Teacher</span>
            </a>

            <!-- Create Class -->
            <a href="{% url 'center:class_create' %}" class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                <div class="bg-purple-100 p-3 rounded-full mb-2">
                    <i class="fas fa-plus text-purple-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Create Class</span>
            </a>

            <!-- Record Payment -->
            <a href="{% url 'center:payment_create' %}" class="flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors">
                <div class="bg-yellow-100 p-3 rounded-full mb-2">
                    <i class="fas fa-dollar-sign text-yellow-600 text-lg"></i>
                </div>
                <span class="text-sm font-medium text-gray-900">Record Payment</span>
            </a>
        </div>
    </div>
</div>

        <!-- Export Section -->
        <div class="section-card rounded-2xl shadow-xl p-8 mb-8">
            <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-2">Export Data</h3>
                <p class="text-gray-600">Download your data in professional Excel format</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Export All Data -->
                <a href="{% url 'center:export_dashboard' %}" class="export-card flex flex-col items-center p-6 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-2xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    <div class="bg-white bg-opacity-20 p-4 rounded-2xl mb-4">
                        <i class="fas fa-download text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-lg mb-2">Complete Report</h4>
                    <p class="text-sm opacity-90 text-center">All data in one comprehensive Excel file</p>
                </a>

                <!-- Export Students -->
                <a href="{% url 'center:export_students' %}" class="export-card flex flex-col items-center p-6 bg-gradient-to-br from-green-500 to-green-600 text-white rounded-2xl hover:from-green-600 hover:to-green-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    <div class="bg-white bg-opacity-20 p-4 rounded-2xl mb-4">
                        <i class="fas fa-user-graduate text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-lg mb-2">Students Data</h4>
                    <p class="text-sm opacity-90 text-center">Complete student information and records</p>
                </a>

                <!-- Export Teachers -->
                <a href="{% url 'center:export_teachers' %}" class="export-card flex flex-col items-center p-6 bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-2xl hover:from-purple-600 hover:to-purple-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    <div class="bg-white bg-opacity-20 p-4 rounded-2xl mb-4">
                        <i class="fas fa-chalkboard-teacher text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-lg mb-2">Teachers Data</h4>
                    <p class="text-sm opacity-90 text-center">Teacher profiles and class assignments</p>
                </a>

                <!-- Export Payments -->
                <a href="{% url 'center:export_payments' %}" class="export-card flex flex-col items-center p-6 bg-gradient-to-br from-yellow-500 to-orange-500 text-white rounded-2xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                    <div class="bg-white bg-opacity-20 p-4 rounded-2xl mb-4">
                        <i class="fas fa-receipt text-2xl"></i>
                    </div>
                    <h4 class="font-bold text-lg mb-2">Payments Data</h4>
                    <p class="text-sm opacity-90 text-center">Financial records and transactions</p>
                </a>
    </div>


            <div class="mt-8 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl border border-gray-200">
                <div class="flex items-center text-gray-700">
                    <div class="bg-blue-100 p-2 rounded-full mr-4">
                        <i class="fas fa-info-circle text-blue-600"></i>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-1">Professional Excel Reports</h4>
                        <p class="text-sm">Excel files include formatted data with headers, borders, and professional styling. Complete report contains all data in separate sheets.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

        <!-- System Status and Recent Student Registrations -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- System Status -->
            <div class="section-card rounded-2xl shadow-xl p-8">
                <h3 class="text-xl font-bold text-gray-800 mb-6">System Status</h3>
                <div class="space-y-6">
                    <!-- Database Status -->
                    <div class="flex justify-between items-center p-4 bg-green-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="bg-green-100 p-2 rounded-lg mr-3">
                                <i class="fas fa-database text-green-600"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Database</span>
                        </div>
                        <span class="bg-green-100 text-green-800 text-sm px-4 py-2 rounded-full font-semibold">Operational</span>
                    </div>

                    <!-- Server Uptime -->
                    <div class="flex justify-between items-center p-4 bg-blue-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                <i class="fas fa-server text-blue-600"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Server Uptime</span>
                        </div>
                        <span class="text-gray-800 font-bold">99.8%</span>
                    </div>

                    <!-- Storage -->
                    <div class="flex justify-between items-center p-4 bg-yellow-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                                <i class="fas fa-hdd text-yellow-600"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Storage</span>
                        </div>
                        <span class="text-gray-800 font-bold">42% used</span>
                    </div>

                    <!-- Last Backup -->
                    <div class="flex justify-between items-center p-4 bg-purple-50 rounded-xl">
                        <div class="flex items-center">
                            <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                <i class="fas fa-cloud-upload-alt text-purple-600"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Last Backup</span>
                        </div>
                        <span class="text-gray-800 font-bold">Dec 15, 2023</span>
                    </div>
                </div>
            </div>

            <!-- Recent Student Registrations -->
            <div class="section-card rounded-2xl shadow-xl p-8">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">Recent Student Registrations</h3>
                    <a href="{% url 'center:student_list' %}" class="text-blue-600 text-sm font-medium hover:text-blue-700 bg-blue-50 px-3 py-1 rounded-lg transition-colors">View All Students</a>
                </div>

                <!-- Table Header -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">
                                <th class="pb-4 font-semibold">Student</th>
                                <th class="pb-4 font-semibold">Level</th>
                                <th class="pb-4 font-semibold">Status</th>
                                <th class="pb-4 font-semibold">Registration Date</th>
                                <th class="pb-4 font-semibold">Assigned Classes</th>
                                <th class="pb-4 font-semibold">Action</th>
                            </tr>
                        </thead>
                <tbody class="divide-y divide-gray-200">
                    <!-- Sophia Martinez -->
                    <tr class="hover:bg-gray-50">
                        <td class="py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-blue-600 font-medium text-sm">SM</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Sophia Martinez</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4">
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded font-medium">Intermediate</span>
                        </td>
                        <td class="py-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded font-medium">Active</span>
                        </td>
                        <td class="py-4 text-sm text-gray-600">Dec 15, 2023</td>
                        <td class="py-4 text-sm text-gray-600">2 classes</td>
                        <td class="py-4">
                            <a href="#" class="text-blue-600 text-sm font-medium hover:text-blue-700">View Profile</a>
                        </td>
                    </tr>

                    <!-- James Wilson -->
                    <tr class="hover:bg-gray-50">
                        <td class="py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <span class="text-yellow-600 font-medium text-sm">JW</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">James Wilson</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4">
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded font-medium">Beginner</span>
                        </td>
                        <td class="py-4">
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded font-medium">Pending</span>
                        </td>
                        <td class="py-4 text-sm text-gray-600">Dec 14, 2023</td>
                        <td class="py-4 text-sm text-gray-600">1 class</td>
                        <td class="py-4">
                            <a href="#" class="text-blue-600 text-sm font-medium hover:text-blue-700">View Profile</a>
                        </td>
                    </tr>

                    <!-- Olivia Kim -->
                    <tr class="hover:bg-gray-50">
                        <td class="py-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <span class="text-purple-600 font-medium text-sm">OK</span>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">Olivia Kim</p>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                </div>
                            </div>
                        </td>
                        <td class="py-4">
                            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded font-medium">Advanced</span>
                        </td>
                        <td class="py-4">
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded font-medium">Active</span>
                        </td>
                        <td class="py-4 text-sm text-gray-600">Dec 12, 2023</td>
                        <td class="py-4 text-sm text-gray-600">3 classes</td>
                        <td class="py-4">
                            <a href="#" class="text-blue-600 text-sm font-medium hover:text-blue-700">View Profile</a>
                        </td>
                    </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Student Distribution Pie Chart
const studentCtx = document.getElementById('studentDistributionChart').getContext('2d');

// Prepare data - only include levels that have students
const studentData = [];
const studentLabels = [];
const studentColors = [];

{% if beginner_count > 0 %}
    studentData.push({{ beginner_count }});
    studentLabels.push('Beginner');
    studentColors.push('#FBBF24');
{% endif %}

{% if elementary_count > 0 %}
    studentData.push({{ elementary_count }});
    studentLabels.push('Elementary');
    studentColors.push('#F59E0B');
{% endif %}

{% if intermediate_count > 0 %}
    studentData.push({{ intermediate_count }});
    studentLabels.push('Intermediate');
    studentColors.push('#3B82F6');
{% endif %}

{% if upper_intermediate_count > 0 %}
    studentData.push({{ upper_intermediate_count }});
    studentLabels.push('Upper-Intermediate');
    studentColors.push('#8B5CF6');
{% endif %}

{% if advanced_count > 0 %}
    studentData.push({{ advanced_count }});
    studentLabels.push('Advanced');
    studentColors.push('#10B981');
{% endif %}

{% if proficiency_count > 0 %}
    studentData.push({{ proficiency_count }});
    studentLabels.push('Proficiency');
    studentColors.push('#EF4444');
{% endif %}

// If no data, show a message
if (studentData.length === 0) {
    studentData.push(1);
    studentLabels.push('No Students');
    studentColors.push('#E5E7EB');
}

const studentChart = new Chart(studentCtx, {
    type: 'doughnut',
    data: {
        labels: studentLabels,
        datasets: [{
            data: studentData,
            backgroundColor: studentColors,
            borderWidth: 3,
            borderColor: '#ffffff',
            cutout: '65%',
            hoverOffset: 10
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#ffffff',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true,
                callbacks: {
                    label: function(context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                    }
                }
            }
        },
        animation: {
            animateRotate: true,
            duration: 1500,
            easing: 'easeInOutQuart'
        },
        onClick: function(event, elements) {
            if (elements.length > 0) {
                const index = elements[0].index;
                const label = studentChart.data.labels[index];
                const value = studentChart.data.datasets[0].data[index];

                // Show alert with student level info
                alert(`${label}: ${value} students`);
            }
        },
        onAnimationComplete: function() {
            // Hide loading spinner when chart is ready
            document.getElementById('studentChartLoader').style.display = 'none';
        }
    }
});

// Revenue Line Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');

// Get real revenue data from Django
const revenueLabels = [{% for label in revenue_labels %}'{{ label }}'{% if not forloop.last %},{% endif %}{% endfor %}];
const revenueData = [{% for amount in revenue_data %}{{ amount }}{% if not forloop.last %},{% endif %}{% endfor %}];

const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: revenueLabels,
        datasets: [{
            label: 'Revenue ($)',
            data: revenueData,
            borderColor: '#10B981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 4,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#10B981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 3,
            pointRadius: 8,
            pointHoverRadius: 10,
            pointHoverBackgroundColor: '#059669',
            pointHoverBorderColor: '#ffffff',
            pointHoverBorderWidth: 3
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#10B981',
                borderWidth: 2,
                cornerRadius: 8,
                displayColors: false,
                callbacks: {
                    label: function(context) {
                        return 'Revenue: $' + context.parsed.y.toLocaleString();
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)',
                    drawBorder: false
                },
                ticks: {
                    color: '#6B7280',
                    font: {
                        size: 12,
                        weight: '500'
                    },
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    },
                    padding: 10
                }
            },
            x: {
                grid: {
                    display: false,
                    drawBorder: false
                },
                ticks: {
                    color: '#6B7280',
                    font: {
                        size: 12,
                        weight: '500'
                    },
                    padding: 10
                }
            }
        },
        animation: {
            duration: 1500,
            easing: 'easeInOutQuart'
        },
        interaction: {
            intersect: false,
            mode: 'index'
        },
        onAnimationComplete: function() {
            // Hide loading spinner when chart is ready
            document.getElementById('revenueChartLoader').style.display = 'none';
        }
    }
});

// Function to refresh charts (can be called when data updates)
function refreshCharts() {
    studentChart.update('active');
    revenueChart.update('active');
}

// Add click handlers to time period buttons
document.addEventListener('DOMContentLoaded', function() {
    // Student chart time period buttons
    const studentButtons = document.querySelectorAll('#studentDistributionChart').closest('.section-card').querySelectorAll('button');
    studentButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            studentButtons.forEach(btn => {
                btn.classList.remove('bg-gradient-to-r', 'from-blue-500', 'to-blue-600', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            // Add active class to clicked button
            this.classList.remove('bg-gray-100', 'text-gray-600');
            this.classList.add('bg-gradient-to-r', 'from-blue-500', 'to-blue-600', 'text-white');

            // Here you could add AJAX call to fetch new data
            console.log('Student chart period changed to:', this.textContent);
        });
    });

    // Revenue chart time period buttons
    const revenueButtons = document.querySelectorAll('#revenueChart').closest('.section-card').querySelectorAll('button');
    revenueButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            revenueButtons.forEach(btn => {
                btn.classList.remove('bg-gradient-to-r', 'from-blue-500', 'to-blue-600', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            // Add active class to clicked button
            this.classList.remove('bg-gray-100', 'text-gray-600');
            this.classList.add('bg-gradient-to-r', 'from-blue-500', 'to-blue-600', 'text-white');

            // Here you could add AJAX call to fetch new data
            console.log('Revenue chart period changed to:', this.textContent);
        });
    });

    // Student table filtering
    const studentFilterButtons = document.querySelectorAll('#allStudentsBtn, #payingStudentsBtn, #recentStudentsBtn');
    const studentTables = document.querySelectorAll('.student-table');

    studentFilterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            studentFilterButtons.forEach(btn => {
                btn.classList.remove('bg-gradient-to-r', 'from-blue-500', 'to-blue-600', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-600');
            });

            // Add active class to clicked button
            this.classList.remove('bg-gray-100', 'text-gray-600');
            this.classList.add('bg-gradient-to-r', 'from-blue-500', 'to-blue-600', 'text-white');

            // Hide all tables
            studentTables.forEach(table => {
                table.classList.add('hidden');
            });

            // Show corresponding table
            if (this.id === 'allStudentsBtn') {
                document.getElementById('allStudentsTable').classList.remove('hidden');
            } else if (this.id === 'payingStudentsBtn') {
                document.getElementById('payingStudentsTable').classList.remove('hidden');
            } else if (this.id === 'recentStudentsBtn') {
                document.getElementById('recentStudentsTable').classList.remove('hidden');
            }

            // Add fade-in animation
            const activeTable = document.querySelector('.student-table:not(.hidden)');
            if (activeTable) {
                activeTable.style.opacity = '0';
                setTimeout(() => {
                    activeTable.style.transition = 'opacity 0.3s ease';
                    activeTable.style.opacity = '1';
                }, 10);
            }
        });
    });

    // Date filtering functionality
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    const applyDateFilterBtn = document.getElementById('applyDateFilter');
    const clearDateFilterBtn = document.getElementById('clearDateFilter');

    // Set default dates (last 30 days to today)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    endDateInput.value = today.toISOString().split('T')[0];
    startDateInput.value = thirtyDaysAgo.toISOString().split('T')[0];

    applyDateFilterBtn.addEventListener('click', function() {
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;

        if (!startDate || !endDate) {
            alert('Please select both start and end dates');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            alert('Start date cannot be after end date');
            return;
        }

        // Filter table rows based on registration date
        filterTablesByDate(startDate, endDate);

        // Show notification
        showNotification(`Filtered students registered between ${startDate} and ${endDate}`);
    });

    clearDateFilterBtn.addEventListener('click', function() {
        startDateInput.value = '';
        endDateInput.value = '';

        // Show all rows
        const allRows = document.querySelectorAll('.student-table tbody tr');
        allRows.forEach(row => {
            row.style.display = '';
        });

        showNotification('Date filter cleared - showing all students');
    });

    function filterTablesByDate(startDate, endDate) {
        const tables = document.querySelectorAll('.student-table');

        tables.forEach(table => {
            const rows = table.querySelectorAll('tbody tr');
            let visibleCount = 0;

            rows.forEach(row => {
                // Skip empty state rows
                if (row.querySelector('td[colspan]')) {
                    return;
                }

                // Find the registration date cell (varies by table)
                let dateCell = null;
                const cells = row.querySelectorAll('td');

                // Look for date in different positions based on table structure
                for (let i = 0; i < cells.length; i++) {
                    const cellText = cells[i].textContent.trim();
                    // Check if cell contains a date pattern (MMM dd, YYYY)
                    if (/[A-Za-z]{3}\s+\d{1,2},\s+\d{4}/.test(cellText)) {
                        dateCell = cells[i];
                        break;
                    }
                }

                if (dateCell) {
                    const dateText = dateCell.textContent.trim();
                    const rowDate = new Date(dateText);
                    const filterStartDate = new Date(startDate);
                    const filterEndDate = new Date(endDate);

                    if (rowDate >= filterStartDate && rowDate <= filterEndDate) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                }
            });

            // Update empty state if no rows visible
            updateEmptyState(table, visibleCount);
        });
    }

    function updateEmptyState(table, visibleCount) {
        let emptyRow = table.querySelector('tbody tr td[colspan]');

        if (visibleCount === 0 && !emptyRow) {
            // Create empty state row
            const tbody = table.querySelector('tbody');
            const emptyRowHtml = `
                <tr class="empty-state-row">
                    <td colspan="6" class="py-8 text-center text-gray-500">
                        <i class="fas fa-calendar-times text-4xl mb-2"></i>
                        <p>No students found for the selected date range</p>
                    </td>
                </tr>
            `;
            tbody.insertAdjacentHTML('beforeend', emptyRowHtml);
        } else if (visibleCount > 0) {
            // Remove empty state row if it exists
            const emptyStateRow = table.querySelector('.empty-state-row');
            if (emptyStateRow) {
                emptyStateRow.remove();
            }
        }
    }

    function showNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <i class="fas fa-info-circle"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Slide in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Slide out and remove after 3 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
});

// Notification Center JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize notification interactions
    initializeNotificationCenter();
});

function initializeNotificationCenter() {
    // Send reminder button handlers
    document.querySelectorAll('.send-reminder-btn').forEach(button => {
        button.addEventListener('click', function() {
            const studentId = this.dataset.studentId;
            const paymentId = this.dataset.paymentId;
            sendPaymentReminder(studentId, paymentId, this);
        });
    });

    // Retry email button handlers
    document.querySelectorAll('.retry-email-btn').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            retryFailedEmail(notificationId, this);
        });
    });

    // Dismiss all notifications
    const dismissAllBtn = document.getElementById('dismissAllBtn');
    if (dismissAllBtn) {
        dismissAllBtn.addEventListener('click', function() {
            dismissAllNotifications();
        });
    }

    // Send all reminders button
    const sendAllBtn = document.getElementById('sendAllRemindersBtn');
    if (sendAllBtn) {
        sendAllBtn.addEventListener('click', function() {
            sendAllReminders(this);
        });
    }
}

function sendPaymentReminder(studentId, paymentId, button) {
    const originalText = button.innerHTML;
    const originalClass = button.className;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Sending...';
    button.disabled = true;
    button.className = button.className.replace(/bg-\w+-\d+/, 'bg-gray-500');

    // Simulate API call (replace with actual AJAX call)
    fetch('/center/send-payment-reminder/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            student_id: studentId,
            payment_id: paymentId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Success state
            button.innerHTML = '<i class="fas fa-check mr-1"></i>Sent!';
            button.className = originalClass.replace(/bg-\w+-\d+/, 'bg-green-600');

            // Show success notification
            showNotification('Payment reminder sent successfully!', 'success');

            // Remove the notification item after a delay
            setTimeout(() => {
                const notificationItem = button.closest('.notification-item');
                if (notificationItem) {
                    notificationItem.style.transition = 'all 0.5s ease';
                    notificationItem.style.transform = 'translateX(100%)';
                    notificationItem.style.opacity = '0';
                    setTimeout(() => {
                        notificationItem.remove();
                        updateNotificationCount();
                    }, 500);
                }
            }, 2000);
        } else {
            // Error state
            button.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>Failed';
            button.className = originalClass.replace(/bg-\w+-\d+/, 'bg-red-600');
            showNotification('Failed to send reminder: ' + (data.error || 'Unknown error'), 'error');

            // Reset button after delay
            setTimeout(() => {
                button.innerHTML = originalText;
                button.className = originalClass;
                button.disabled = false;
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>Error';
        button.className = originalClass.replace(/bg-\w+-\d+/, 'bg-red-600');
        showNotification('Network error occurred', 'error');

        // Reset button after delay
        setTimeout(() => {
            button.innerHTML = originalText;
            button.className = originalClass;
            button.disabled = false;
        }, 3000);
    });
}

function retryFailedEmail(notificationId, button) {
    const originalText = button.innerHTML;

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Retrying...';
    button.disabled = true;

    // Simulate API call for retry
    fetch('/center/retry-email-notification/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            notification_id: notificationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.innerHTML = '<i class="fas fa-check mr-1"></i>Sent!';
            button.className = button.className.replace('bg-purple-600', 'bg-green-600');
            showNotification('Email sent successfully!', 'success');

            // Remove the notification item
            setTimeout(() => {
                const notificationItem = button.closest('.notification-item');
                if (notificationItem) {
                    notificationItem.style.transition = 'all 0.5s ease';
                    notificationItem.style.transform = 'translateX(100%)';
                    notificationItem.style.opacity = '0';
                    setTimeout(() => {
                        notificationItem.remove();
                        updateNotificationCount();
                    }, 500);
                }
            }, 2000);
        } else {
            button.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>Failed';
            showNotification('Failed to retry email: ' + (data.error || 'Unknown error'), 'error');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>Error';
        showNotification('Network error occurred', 'error');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 3000);
    });
}

function sendAllReminders(button) {
    const originalText = button.innerHTML;

    if (!confirm('Are you sure you want to send all payment reminders?')) {
        return;
    }

    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending All...';
    button.disabled = true;
    button.className = button.className.replace('bg-green-600', 'bg-gray-500');

    // Send all reminders
    fetch('/center/send-all-reminders/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.innerHTML = '<i class="fas fa-check mr-2"></i>All Sent!';
            button.className = button.className.replace('bg-gray-500', 'bg-green-600');
            showNotification(`Successfully sent ${data.count} payment reminders!`, 'success');

            // Refresh the page after a delay to show updated notifications
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        } else {
            button.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Some Failed';
            showNotification('Some reminders failed to send: ' + (data.error || 'Unknown error'), 'error');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.className = button.className.replace('bg-gray-500', 'bg-green-600');
                button.disabled = false;
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Error';
        showNotification('Network error occurred', 'error');

        setTimeout(() => {
            button.innerHTML = originalText;
            button.className = button.className.replace('bg-gray-500', 'bg-green-600');
            button.disabled = false;
        }, 3000);
    });
}

function dismissAllNotifications() {
    if (!confirm('Are you sure you want to dismiss all notifications?')) {
        return;
    }

    const notificationCenter = document.querySelector('.notification-center');
    if (notificationCenter) {
        notificationCenter.style.transition = 'all 0.5s ease';
        notificationCenter.style.transform = 'translateY(-20px)';
        notificationCenter.style.opacity = '0';

        setTimeout(() => {
            notificationCenter.remove();
            showNotification('All notifications dismissed', 'success');
        }, 500);
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} mr-2"></i>
        ${message}
    `;

    // Insert at the top of the dashboard
    const dashboard = document.querySelector('.dashboard-container .container');
    if (dashboard) {
        dashboard.insertBefore(notification, dashboard.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.style.transition = 'all 0.5s ease';
            notification.style.transform = 'translateY(-20px)';
            notification.style.opacity = '0';
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 5000);
    }
}

function updateNotificationCount() {
    const notificationItems = document.querySelectorAll('.notification-item');
    const badge = document.querySelector('.notification-badge');

    if (badge) {
        const count = notificationItems.length;
        badge.textContent = count;

        if (count === 0) {
            const notificationCenter = document.querySelector('.notification-center');
            if (notificationCenter) {
                notificationCenter.style.transition = 'all 0.5s ease';
                notificationCenter.style.transform = 'translateY(-20px)';
                notificationCenter.style.opacity = '0';
                setTimeout(() => {
                    notificationCenter.remove();
                }, 500);
            }
        }
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
