{% extends 'center/base.html' %}

{% block title %}Dashboard - English Language Center{% endblock %}
{% block page_title %}Dashboard Overview{% endblock %}

{% block extra_css %}
.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
.progress-bar {
    height: 4px;
    border-radius: 2px;
    overflow: hidden;
}
.progress-fill {
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
}
{% endblock %}

{% block content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- Students Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6 transition duration-300">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500">Total Students</p>
                <h3 class="text-3xl font-bold mt-2">{{ total_students }}</h3>
                <div class="flex items-center mt-2">
                    <span class="text-green-500 mr-1"><i class="fas fa-arrow-up"></i> 12%</span>
                    <span class="text-gray-500 text-sm">from last month</span>
                </div>
            </div>
            <div class="bg-blue-100 p-3 rounded-lg">
                <i class="fas fa-user-graduate text-blue-500 text-2xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-500 mb-1">
                <span>Active</span>
                <span>{{ total_students }}</span>
            </div>
            <div class="progress-bar bg-gray-200">
                <div class="progress-fill bg-blue-500" style="width: 80%"></div>
            </div>
        </div>
    </div>

    <!-- Teachers Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6 transition duration-300">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500">Teachers</p>
                <h3 class="text-3xl font-bold mt-2">{{ total_teachers }}</h3>
                <div class="flex items-center mt-2">
                    <span class="text-green-500 mr-1"><i class="fas fa-arrow-up"></i> 5%</span>
                    <span class="text-gray-500 text-sm">from last month</span>
                </div>
            </div>
            <div class="bg-green-100 p-3 rounded-lg">
                <i class="fas fa-chalkboard-teacher text-green-500 text-2xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-500 mb-1">
                <span>Active</span>
                <span>{{ total_teachers }}</span>
            </div>
            <div class="progress-bar bg-gray-200">
                <div class="progress-fill bg-green-500" style="width: 83%"></div>
            </div>
        </div>
    </div>

    <!-- Classes Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6 transition duration-300">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500">Active Classes</p>
                <h3 class="text-3xl font-bold mt-2">{{ total_classes }}</h3>
                <div class="flex items-center mt-2">
                    <span class="text-green-500 mr-1"><i class="fas fa-arrow-up"></i> 8%</span>
                    <span class="text-gray-500 text-sm">from last month</span>
                </div>
            </div>
            <div class="bg-purple-100 p-3 rounded-lg">
                <i class="fas fa-users text-purple-500 text-2xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-500 mb-1">
                <span>Capacity</span>
                <span>82%</span>
            </div>
            <div class="progress-bar bg-gray-200">
                <div class="progress-fill bg-purple-500" style="width: 82%"></div>
            </div>
        </div>
    </div>

    <!-- Revenue Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6 transition duration-300">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500">Monthly Revenue</p>
                <h3 class="text-3xl font-bold mt-2">${{ monthly_revenue|floatformat:2 }}</h3>
                <div class="flex items-center mt-2">
                    <span class="text-green-500 mr-1"><i class="fas fa-arrow-up"></i> 15%</span>
                    <span class="text-gray-500 text-sm">from last month</span>
                </div>
            </div>
            <div class="bg-yellow-100 p-3 rounded-lg">
                <i class="fas fa-dollar-sign text-yellow-500 text-2xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-500 mb-1">
                <span>Target</span>
                <span>$15,000</span>
            </div>
            <div class="progress-bar bg-gray-200">
                <div class="progress-fill bg-yellow-500" style="width: {% widthratio monthly_revenue 15000 100 %}%"></div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Student Distribution Chart -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold">Student Distribution by Level</h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 bg-gray-100 rounded-lg text-sm">Monthly</button>
                <button class="px-3 py-1 bg-primary text-white rounded-lg text-sm">Yearly</button>
            </div>
        </div>
        <div class="space-y-3">
            {% for level_data in students_by_level %}
            <div class="flex items-center justify-between">
                <span class="text-gray-700">{{ level_data.level|title }}</span>
                <div class="flex items-center">
                    <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                        <div class="bg-primary h-2 rounded-full" style="width: {% widthratio level_data.count total_students 100 %}%"></div>
                    </div>
                    <span class="font-bold">{{ level_data.count }}</span>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No student data available</p>
            {% endfor %}
        </div>
    </div>

    <!-- Revenue Chart -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold">Revenue Overview</h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 bg-gray-100 rounded-lg text-sm">Monthly</button>
                <button class="px-3 py-1 bg-primary text-white rounded-lg text-sm">Yearly</button>
            </div>
        </div>
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-3xl font-bold text-green-600">${{ monthly_revenue|floatformat:2 }}</p>
                    <p class="text-gray-500">This month</p>
                </div>
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-chart-line text-2xl"></i>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                        <span class="text-gray-700">Overdue Payments</span>
                    </div>
                    <span class="font-bold text-red-600">{{ overdue_payments }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                        <span class="text-gray-700">Due This Week</span>
                    </div>
                    <span class="font-bold text-yellow-600">{{ upcoming_payments }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity Section -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- Upcoming Classes -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold">Upcoming Classes</h3>
            <a href="{% url 'center:class_list' %}" class="text-primary text-sm">View All</a>
        </div>

        <div class="space-y-4">
            {% for class in classes_by_level|slice:":3" %}
            <div class="flex items-start border-b pb-4">
                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                    <i class="fas fa-users text-blue-500"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium">{{ class.level|title }} Class</h4>
                    <p class="text-sm text-gray-500">{{ class.count }} students enrolled</p>
                    <div class="flex items-center mt-1">
                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Active</span>
                    </div>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No classes available</p>
            {% endfor %}
        </div>
    </div>

    <!-- Recent Students -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold">Recent Students</h3>
            <a href="{% url 'center:student_list' %}" class="text-primary text-sm">View All</a>
        </div>

        <div class="space-y-4">
            {% for student in recent_students %}
            <div class="flex items-start border-b pb-4">
                <div class="bg-green-100 p-2 rounded-lg mr-3">
                    <i class="fas fa-user-graduate text-green-500"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium">{{ student.full_name }}</h4>
                    <p class="text-sm text-gray-500">{{ student.get_level_display }}</p>
                    <div class="flex items-center mt-1">
                        <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">{{ student.registration_date|date:"M d" }}</span>
                    </div>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No recent students</p>
            {% endfor %}
        </div>
    </div>

    <!-- Recent Payments -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold">Recent Payments</h3>
            <a href="{% url 'center:payment_list' %}" class="text-primary text-sm">View All</a>
        </div>

        <div class="space-y-4">
            {% for payment in recent_payments %}
            <div class="flex items-start border-b pb-4">
                <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                    <i class="fas fa-dollar-sign text-yellow-500"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium">${{ payment.amount }}</h4>
                    <p class="text-sm text-gray-500">{{ payment.student.full_name }}</p>
                    <div class="flex items-center mt-1">
                        {% if payment.is_confirmed %}
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Confirmed</span>
                        {% else %}
                            <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Pending</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No recent payments</p>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Level Distribution -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Students by Level -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold mb-4">Students by Level</h3>
        <div class="space-y-3">
            {% for level_data in students_by_level %}
            <div class="flex items-center justify-between">
                <span class="text-gray-700">{{ level_data.level|title }}</span>
                <div class="flex items-center">
                    <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                        <div class="bg-primary h-2 rounded-full" style="width: {% widthratio level_data.count total_students 100 %}%"></div>
                    </div>
                    <span class="font-bold">{{ level_data.count }}</span>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No student data available</p>
            {% endfor %}
        </div>
    </div>
    
    <!-- Classes by Level -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <h3 class="text-lg font-bold mb-4">Classes by Level</h3>
        <div class="space-y-3">
            {% for class_data in classes_by_level %}
            <div class="flex items-center justify-between">
                <span class="text-gray-700">{{ class_data.level|title }}</span>
                <div class="flex items-center">
                    <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                        <div class="bg-accent h-2 rounded-full" style="width: {% widthratio class_data.count total_classes 100 %}%"></div>
                    </div>
                    <span class="font-bold">{{ class_data.count }}</span>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No class data available</p>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="fixed bottom-6 right-6 space-y-3">
    <a href="{% url 'center:student_create' %}" class="block bg-primary text-white p-3 rounded-full shadow-lg hover:bg-secondary transition duration-300">
        <i class="fas fa-user-plus text-xl"></i>
    </a>
    <a href="{% url 'center:payment_create' %}" class="block bg-accent text-white p-3 rounded-full shadow-lg hover:bg-green-600 transition duration-300">
        <i class="fas fa-dollar-sign text-xl"></i>
    </a>
</div>
{% endblock %}
