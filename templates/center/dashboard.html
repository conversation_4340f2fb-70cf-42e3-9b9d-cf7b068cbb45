{% extends 'center/base.html' %}

{% block title %}Dashboard - English Language Center{% endblock %}
{% block page_title %}Dashboard Overview{% endblock %}

{% block extra_css %}
<style>
.stat-card {
    transition: all 0.3s ease;
}
.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}
.progress-bar {
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    background-color: #e5e7eb;
}
.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 0.8s ease;
}
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}
.metric-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
}
.metric-up {
    background-color: #dcfce7;
    color: #166534;
}
.metric-down {
    background-color: #fef2f2;
    color: #dc2626;
}
</style>
{% endblock %}

{% block content %}
<!-- Stats Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Students Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <p class="text-gray-500 text-sm font-medium">Total Students</p>
                <h3 class="text-3xl font-bold text-gray-900 mt-2">{{ total_students }}</h3>
                <div class="flex items-center mt-3">
                    <span class="metric-badge metric-up">
                        <i class="fas fa-arrow-up mr-1"></i> 12%
                    </span>
                    <span class="text-gray-500 text-sm ml-2">from last month</span>
                </div>
            </div>
            <div class="bg-blue-100 p-3 rounded-lg">
                <i class="fas fa-user-graduate text-blue-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Active</span>
                <span>{{ active_students|default:total_students }}</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill bg-blue-500" style="width: {% widthratio active_students|default:total_students total_students 100 %}%"></div>
            </div>
        </div>
    </div>

    <!-- Teachers Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <p class="text-gray-500 text-sm font-medium">Teachers</p>
                <h3 class="text-3xl font-bold text-gray-900 mt-2">{{ total_teachers }}</h3>
                <div class="flex items-center mt-3">
                    <span class="metric-badge metric-up">
                        <i class="fas fa-arrow-up mr-1"></i> 5%
                    </span>
                    <span class="text-gray-500 text-sm ml-2">from last month</span>
                </div>
            </div>
            <div class="bg-green-100 p-3 rounded-lg">
                <i class="fas fa-chalkboard-teacher text-green-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Active</span>
                <span>{{ active_teachers|default:total_teachers }}</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill bg-green-500" style="width: {% widthratio active_teachers|default:total_teachers total_teachers 100 %}%"></div>
            </div>
        </div>
    </div>

    <!-- Classes Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <p class="text-gray-500 text-sm font-medium">Active Classes</p>
                <h3 class="text-3xl font-bold text-gray-900 mt-2">{{ total_classes }}</h3>
                <div class="flex items-center mt-3">
                    <span class="metric-badge metric-up">
                        <i class="fas fa-arrow-up mr-1"></i> 8%
                    </span>
                    <span class="text-gray-500 text-sm ml-2">from last month</span>
                </div>
            </div>
            <div class="bg-purple-100 p-3 rounded-lg">
                <i class="fas fa-users text-purple-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Capacity</span>
                <span>82%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill bg-purple-500" style="width: 82%"></div>
            </div>
        </div>
    </div>

    <!-- Revenue Card -->
    <div class="stat-card bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <p class="text-gray-500 text-sm font-medium">Monthly Revenue</p>
                <h3 class="text-3xl font-bold text-gray-900 mt-2">${{ monthly_revenue|floatformat:0|default:"12,450" }}</h3>
                <div class="flex items-center mt-3">
                    <span class="metric-badge metric-up">
                        <i class="fas fa-arrow-up mr-1"></i> 15%
                    </span>
                    <span class="text-gray-500 text-sm ml-2">from last month</span>
                </div>
            </div>
            <div class="bg-yellow-100 p-3 rounded-lg">
                <i class="fas fa-dollar-sign text-yellow-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span>Target</span>
                <span>$15,000</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill bg-yellow-500" style="width: {% widthratio monthly_revenue|default:12450 15000 100 %}%"></div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Student Distribution Chart -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold text-gray-900">Student Distribution by Level</h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">Monthly</button>
                <button class="px-3 py-1 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">Yearly</button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="studentDistributionChart"></canvas>
        </div>
        <div class="grid grid-cols-2 gap-4 mt-6">
            <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Beginner</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Intermediate</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Upper-Intermediate</span>
            </div>
            <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Advanced</span>
            </div>
        </div>
    </div>

    <!-- Revenue Chart -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-bold text-gray-900">Revenue Overview</h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 bg-gray-100 text-gray-600 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">Monthly</button>
                <button class="px-3 py-1 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">Yearly</button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="revenueChart"></canvas>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Student Distribution Pie Chart
const studentCtx = document.getElementById('studentDistributionChart').getContext('2d');
const studentChart = new Chart(studentCtx, {
    type: 'doughnut',
    data: {
        labels: ['Beginner', 'Intermediate', 'Upper-Intermediate', 'Advanced'],
        datasets: [{
            data: [{{ beginner_count|default:45 }}, {{ intermediate_count|default:85 }}, {{ upper_intermediate_count|default:65 }}, {{ advanced_count|default:53 }}],
            backgroundColor: [
                '#FBBF24', // Yellow
                '#3B82F6', // Blue
                '#8B5CF6', // Purple
                '#10B981'  // Green
            ],
            borderWidth: 0,
            cutout: '60%'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Revenue Line Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: ['Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
            label: 'Revenue',
            data: [8000, 9500, 10200, 11000, 11800, 12450],
            borderColor: '#10B981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#10B981',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: '#f3f4f6'
                },
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});
</script>
{% endblock %}
