{% extends 'center/base.html' %}

{% block title %}Delete Student - English Language Center{% endblock %}
{% block page_title %}Delete Student{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="text-center mb-6">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-900">Delete Student</h3>
            <p class="text-gray-500 mt-2">Are you sure you want to delete this student? This action cannot be undone.</p>
        </div>

        <!-- Student Info -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <div class="bg-gray-200 border-2 border-dashed rounded-xl w-12 h-12 mr-4 flex items-center justify-center">
                    <i class="fas fa-user-graduate text-gray-400"></i>
                </div>
                <div>
                    <h4 class="font-bold">{{ student.full_name }}</h4>
                    <p class="text-sm text-gray-500">{{ student.email }}</p>
                    <p class="text-sm text-gray-500">{{ student.get_level_display }} • Registered {{ student.registration_date|date:"M d, Y" }}</p>
                </div>
            </div>
        </div>

        <!-- Warning -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>Deleting this student will also:</p>
                        <ul class="list-disc list-inside mt-1">
                            <li>Remove them from all enrolled classes</li>
                            <li>Keep payment records for accounting purposes</li>
                            <li>Remove access to the student portal (if applicable)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confirmation Form -->
        <form method="post">
            {% csrf_token %}
            <div class="flex justify-end space-x-4">
                <a href="{% url 'center:student_detail' student.pk %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300">
                    <i class="fas fa-trash mr-2"></i> Delete Student
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
