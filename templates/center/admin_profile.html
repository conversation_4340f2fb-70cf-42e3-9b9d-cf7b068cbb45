{% extends 'center/base.html' %}

{% block title %}Admin Profile{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Admin Profile</h1>
                    <p class="mt-2 text-gray-600">Manage your account information</p>
                </div>
                <a href="{% url 'center:admin_profile_edit' %}" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-edit mr-2"></i>Edit Profile
                </a>
            </div>
        </div>

        <!-- Profile Card -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <!-- Profile Header -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8">
                <div class="flex items-center">
                    <div class="bg-white rounded-full p-4 mr-6">
                        <i class="fas fa-user-shield text-blue-600 text-4xl"></i>
                    </div>
                    <div class="text-white">
                        <h2 class="text-2xl font-bold">{{ user.first_name }} {{ user.last_name }}</h2>
                        <p class="text-blue-100">System Administrator</p>
                        <p class="text-blue-100">{{ user.email }}</p>
                    </div>
                </div>
            </div>

            <!-- Profile Details -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Personal Information -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Personal Information</h3>
                        
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Username</label>
                                <p class="text-gray-900 font-medium">{{ user.username }}</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-500">First Name</label>
                                <p class="text-gray-900 font-medium">{{ user.first_name|default:"Not set" }}</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Last Name</label>
                                <p class="text-gray-900 font-medium">{{ user.last_name|default:"Not set" }}</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Email Address</label>
                                <p class="text-gray-900 font-medium">{{ user.email|default:"Not set" }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Account Information -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-semibold text-gray-900 border-b pb-2">Account Information</h3>
                        
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Account Status</label>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Active
                                </span>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-500">User Type</label>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                    <i class="fas fa-shield-alt mr-1"></i>Administrator
                                </span>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Date Joined</label>
                                <p class="text-gray-900 font-medium">{{ user.date_joined|date:"F d, Y" }}</p>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Last Login</label>
                                <p class="text-gray-900 font-medium">{{ user.last_login|date:"F d, Y g:i A"|default:"Never" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-blue-100 rounded-full p-3 mr-4">
                        <i class="fas fa-user-graduate text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Total Students</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_students|default:0 }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-green-100 rounded-full p-3 mr-4">
                        <i class="fas fa-chalkboard-teacher text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Total Teachers</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_teachers|default:0 }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-purple-100 rounded-full p-3 mr-4">
                        <i class="fas fa-book text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Active Classes</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_classes|default:0 }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex items-center">
                    <div class="bg-yellow-100 rounded-full p-3 mr-4">
                        <i class="fas fa-credit-card text-yellow-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Total Payments</p>
                        <p class="text-2xl font-bold text-gray-900">{{ total_payments|default:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex space-x-4">
            <a href="{% url 'center:admin_profile_edit' %}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition duration-200">
                <i class="fas fa-edit mr-2"></i>Edit Profile
            </a>
            <a href="{% url 'center:settings' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition duration-200">
                <i class="fas fa-cog mr-2"></i>Settings
            </a>
            <a href="{% url 'center:dashboard' %}" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition duration-200">
                <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}
