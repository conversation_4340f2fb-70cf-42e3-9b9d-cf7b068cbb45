{% extends 'center/base.html' %}

{% block title %}{{ class.class_name }} - Class Details{% endblock %}
{% block page_title %}Class Details{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Class Header -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-2xl font-bold">{{ class.class_name }}</h1>
                <p class="text-gray-500">{{ class.get_level_display }} Level</p>
                <div class="flex items-center mt-2">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm mr-3">{{ class.current_enrollment }}/{{ class.capacity }} Students</span>
                    {% if class.is_active %}
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Active</span>
                    {% else %}
                        <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">Inactive</span>
                    {% endif %}
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'center:class_update' class.pk %}" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    <i class="fas fa-edit mr-2"></i> Edit
                </a>
                <a href="{% url 'center:class_delete' class.pk %}" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300" onclick="return confirm('Are you sure you want to delete this class?')">
                    <i class="fas fa-trash mr-2"></i> Delete
                </a>
                <a href="{% url 'center:class_list' %}" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Class Information -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4">Class Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-500 text-sm">Class Name</label>
                        <p class="font-medium">{{ class.class_name }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Level</label>
                        <p class="font-medium">{{ class.get_level_display }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Teacher</label>
                        <p class="font-medium">
                            <a href="{% url 'center:teacher_detail' class.teacher.pk %}" class="text-primary hover:text-secondary">
                                {{ class.teacher.name }}
                            </a>
                        </p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Schedule</label>
                        <p class="font-medium">{{ class.schedule_display }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Capacity</label>
                        <p class="font-medium">{{ class.capacity }} students</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Available Spots</label>
                        <p class="font-medium">
                            {% if class.is_full %}
                                <span class="text-red-600">Class Full</span>
                            {% else %}
                                <span class="text-green-600">{{ class.available_spots }} spots</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Enrolled Students -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">Enrolled Students ({{ class.current_enrollment }})</h3>
                    {% if not class.is_full %}
                        <button onclick="openEnrollmentModal()" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                            <i class="fas fa-plus mr-2"></i> Add Student
                        </button>
                    {% else %}
                        <span class="px-4 py-2 bg-gray-400 text-white rounded-lg">
                            <i class="fas fa-ban mr-2"></i> Class Full
                        </span>
                    {% endif %}
                </div>
                {% if class.students.all %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {% for student in class.students.all %}
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-200">
                            <div class="flex items-center">
                                <div class="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10 mr-3 flex items-center justify-center">
                                    <i class="fas fa-user-graduate text-gray-400"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">
                                        <a href="{% url 'center:student_detail' student.pk %}" class="text-primary hover:text-secondary">
                                            {{ student.full_name }}
                                        </a>
                                    </h4>
                                    <p class="text-sm text-gray-500">{{ student.email }}</p>
                                    <p class="text-xs text-gray-400">{{ student.phone_number }}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                    {{ student.get_level_display }}
                                </span>
                                <button onclick="removeStudent({{ student.id }}, '{{ student.full_name }}')"
                                        class="p-1 text-red-600 hover:bg-red-100 rounded transition duration-200"
                                        title="Remove from class">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-8">No students enrolled yet</p>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Stats</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500">Enrollment</span>
                        <span class="font-bold text-blue-600">{{ class.current_enrollment }}/{{ class.capacity }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Capacity</span>
                        <span class="font-bold">
                            {% widthratio class.current_enrollment class.capacity 100 %}%
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Available</span>
                        <span class="font-bold text-green-600">{{ class.available_spots }}</span>
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-500 mb-1">
                        <span>Enrollment Progress</span>
                        <span>{% widthratio class.current_enrollment class.capacity 100 %}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: {% widthratio class.current_enrollment class.capacity 100 %}%"></div>
                    </div>
                </div>
            </div>

            <!-- Teacher Info -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Teacher</h3>
                <div class="flex items-center">
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-12 h-12 mr-4 flex items-center justify-center">
                        <i class="fas fa-chalkboard-teacher text-gray-400"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">
                            <a href="{% url 'center:teacher_detail' class.teacher.pk %}" class="text-primary hover:text-secondary">
                                {{ class.teacher.name }}
                            </a>
                        </h4>
                        <p class="text-sm text-gray-500">{{ class.teacher.subject_specialization }}</p>
                        <p class="text-sm text-gray-500">{{ class.teacher.email }}</p>
                    </div>
                </div>
            </div>

            <!-- Schedule Info -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Schedule</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <i class="fas fa-calendar text-gray-400 mr-3"></i>
                        <span>{{ class.get_days_display }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-clock text-gray-400 mr-3"></i>
                        <span>{{ class.start_time|time:"H:i" }} - {{ class.end_time|time:"H:i" }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-repeat text-gray-400 mr-3"></i>
                        <span>{{ class.sessions_per_week }} session{{ class.sessions_per_week|pluralize }} per week</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-hourglass-half text-gray-400 mr-3"></i>
                        <span>1.5 hours per session</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Actions</h3>
                <div class="space-y-2">
                    {% if not class.is_full %}
                        <button onclick="openEnrollmentModal()" class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg text-center hover:bg-blue-200 transition duration-300">
                            <i class="fas fa-user-plus mr-2"></i> Add Student
                        </button>
                    {% else %}
                        <div class="block w-full px-4 py-2 bg-gray-100 text-gray-500 rounded-lg text-center">
                            <i class="fas fa-ban mr-2"></i> Class Full ({{ class.current_enrollment }}/{{ class.capacity }})
                        </div>
                    {% endif %}
                    <a href="{% url 'center:class_update' class.pk %}" class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-center hover:bg-gray-200 transition duration-300">
                        <i class="fas fa-edit mr-2"></i> Edit Class
                    </a>
                    <a href="{% url 'center:export_dashboard' %}" class="block w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg text-center hover:bg-green-200 transition duration-300">
                        <i class="fas fa-file-export mr-2"></i> Export Data
                    </a>
                </div>

                {% if class.current_enrollment > 0 %}
                <!-- Deletion Warning -->
                <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-yellow-600 mt-0.5 mr-2"></i>
                        <div class="text-sm">
                            <p class="font-medium text-yellow-800">Class Deletion</p>
                            <p class="text-yellow-700 mt-1">
                                This class has {{ class.current_enrollment }} enrolled student(s).
                                To delete this class, first remove all students or deactivate the class instead.
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Student Enrollment Modal -->
<div id="enrollmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-96 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-purple-600">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-white">
                        <i class="fas fa-user-plus mr-2"></i>Add Student to {{ class.class_name }}
                    </h3>
                    <button onclick="closeEnrollmentModal()" class="text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="p-6">
                <!-- Search and Filter -->
                <div class="mb-4 space-y-3">
                    <div class="flex space-x-3">
                        <input type="text" id="studentSearch" placeholder="Search students by name or email..."
                               class="flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                               onkeyup="searchStudents()">
                        <select id="levelFilter" class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                                onchange="searchStudents()">
                            <option value="">All Levels</option>
                            <option value="{{ class.level }}" selected>{{ class.get_level_display }} (Recommended)</option>
                            <option value="BEGINNER">Beginner</option>
                            <option value="ELEMENTARY">Elementary</option>
                            <option value="INTERMEDIATE">Intermediate</option>
                            <option value="UPPER_INTERMEDIATE">Upper-Intermediate</option>
                            <option value="ADVANCED">Advanced</option>
                            <option value="PROFICIENCY">Proficiency</option>
                        </select>
                    </div>
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-info-circle mr-1"></i>
                        Available spots: <span id="availableSpots">{{ class.available_spots }}</span> |
                        Class level: {{ class.get_level_display }}
                    </div>
                </div>

                <!-- Students List -->
                <div id="studentsContainer" class="max-h-64 overflow-y-auto">
                    <div id="loadingSpinner" class="text-center py-8">
                        <i class="fas fa-spinner fa-spin text-gray-400 text-2xl"></i>
                        <p class="text-gray-500 mt-2">Loading students...</p>
                    </div>
                    <div id="studentsList" class="space-y-2 hidden"></div>
                    <div id="noStudents" class="text-center py-8 hidden">
                        <i class="fas fa-users text-gray-300 text-3xl"></i>
                        <p class="text-gray-500 mt-2">No available students found</p>
                    </div>
                </div>

                <!-- Enrollment Form -->
                <form id="enrollmentForm" method="post" action="{% url 'center:enroll_student_in_class' class.id %}" class="hidden mt-4">
                    {% csrf_token %}
                    <input type="hidden" id="selectedStudentId" name="student_id">
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeEnrollmentModal()"
                                class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary">
                            <i class="fas fa-plus mr-2"></i>Enroll Student
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Remove Student Form (Hidden) -->
<form id="removeStudentForm" method="post" style="display: none;">
    {% csrf_token %}
</form>

<script>
let availableStudents = [];
let selectedStudent = null;

function openEnrollmentModal() {
    document.getElementById('enrollmentModal').classList.remove('hidden');
    loadAvailableStudents();
}

function closeEnrollmentModal() {
    document.getElementById('enrollmentModal').classList.add('hidden');
    document.getElementById('studentSearch').value = '';
    document.getElementById('levelFilter').value = '{{ class.level }}';
    selectedStudent = null;
    document.getElementById('enrollmentForm').classList.add('hidden');
}

function loadAvailableStudents() {
    const container = document.getElementById('studentsContainer');
    const loading = document.getElementById('loadingSpinner');
    const studentsList = document.getElementById('studentsList');
    const noStudents = document.getElementById('noStudents');

    loading.classList.remove('hidden');
    studentsList.classList.add('hidden');
    noStudents.classList.add('hidden');

    const level = document.getElementById('levelFilter').value;
    const search = document.getElementById('studentSearch').value;

    let url = `{% url 'center:get_available_students_for_class' class.id %}?`;
    if (level) url += `level=${level}&`;
    if (search) url += `search=${encodeURIComponent(search)}&`;

    fetch(url)
        .then(response => response.json())
        .then(data => {
            availableStudents = data.students;
            displayStudents();
            document.getElementById('availableSpots').textContent = data.available_spots;
            loading.classList.add('hidden');
        })
        .catch(error => {
            console.error('Error loading students:', error);
            loading.classList.add('hidden');
            noStudents.classList.remove('hidden');
        });
}

function displayStudents() {
    const studentsList = document.getElementById('studentsList');
    const noStudents = document.getElementById('noStudents');

    if (availableStudents.length === 0) {
        studentsList.classList.add('hidden');
        noStudents.classList.remove('hidden');
        return;
    }

    studentsList.innerHTML = '';
    availableStudents.forEach(student => {
        const studentDiv = document.createElement('div');
        studentDiv.className = 'flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition duration-200';
        studentDiv.onclick = () => selectStudent(student);

        const levelMatch = student.level === '{{ class.get_level_display }}';
        const levelBadgeClass = levelMatch ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';

        studentDiv.innerHTML = `
            <div class="flex items-center">
                <div class="bg-gray-200 rounded-full w-8 h-8 mr-3 flex items-center justify-center">
                    <i class="fas fa-user text-gray-400 text-sm"></i>
                </div>
                <div>
                    <h4 class="font-medium text-gray-900">${student.name}</h4>
                    <p class="text-sm text-gray-500">${student.email}</p>
                    ${student.phone ? `<p class="text-xs text-gray-400">${student.phone}</p>` : ''}
                </div>
            </div>
            <div class="text-right">
                <span class="px-2 py-1 ${levelBadgeClass} rounded-full text-xs">
                    ${student.level}
                </span>
                ${levelMatch ? '<i class="fas fa-check-circle text-green-500 ml-2" title="Level matches class"></i>' : ''}
            </div>
        `;

        studentsList.appendChild(studentDiv);
    });

    studentsList.classList.remove('hidden');
    noStudents.classList.add('hidden');
}

function selectStudent(student) {
    selectedStudent = student;
    document.getElementById('selectedStudentId').value = student.id;

    // Highlight selected student
    document.querySelectorAll('#studentsList > div').forEach(div => {
        div.classList.remove('bg-blue-100', 'border-blue-300');
        div.classList.add('border-gray-200');
    });

    event.currentTarget.classList.add('bg-blue-100', 'border-blue-300');
    event.currentTarget.classList.remove('border-gray-200');

    document.getElementById('enrollmentForm').classList.remove('hidden');
}

function searchStudents() {
    loadAvailableStudents();
}

function removeStudent(studentId, studentName) {
    if (confirm(`Are you sure you want to remove ${studentName} from this class?`)) {
        const form = document.getElementById('removeStudentForm');
        form.action = `{% url 'center:remove_student_from_class' class.id 0 %}`.replace('0', studentId);
        form.submit();
    }
}

// Close modal when clicking outside
document.getElementById('enrollmentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEnrollmentModal();
    }
});
</script>

{% endblock %}
