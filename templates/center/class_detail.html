{% extends 'center/base.html' %}

{% block title %}{{ class.class_name }} - Class Details{% endblock %}
{% block page_title %}Class Details{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Class Header -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-2xl font-bold">{{ class.class_name }}</h1>
                <p class="text-gray-500">{{ class.get_level_display }} Level</p>
                <div class="flex items-center mt-2">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm mr-3">{{ class.current_enrollment }}/{{ class.capacity }} Students</span>
                    {% if class.is_active %}
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Active</span>
                    {% else %}
                        <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">Inactive</span>
                    {% endif %}
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'center:class_update' class.pk %}" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    <i class="fas fa-edit mr-2"></i> Edit
                </a>
                <a href="{% url 'center:class_list' %}" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Class Information -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4">Class Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-500 text-sm">Class Name</label>
                        <p class="font-medium">{{ class.class_name }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Level</label>
                        <p class="font-medium">{{ class.get_level_display }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Teacher</label>
                        <p class="font-medium">
                            <a href="{% url 'center:teacher_detail' class.teacher.pk %}" class="text-primary hover:text-secondary">
                                {{ class.teacher.name }}
                            </a>
                        </p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Schedule</label>
                        <p class="font-medium">{{ class.schedule_display }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Capacity</label>
                        <p class="font-medium">{{ class.capacity }} students</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Available Spots</label>
                        <p class="font-medium">
                            {% if class.is_full %}
                                <span class="text-red-600">Class Full</span>
                            {% else %}
                                <span class="text-green-600">{{ class.available_spots }} spots</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Enrolled Students -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-bold">Enrolled Students ({{ class.current_enrollment }})</h3>
                    {% if not class.is_full %}
                        <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                            <i class="fas fa-plus mr-2"></i> Add Student
                        </button>
                    {% endif %}
                </div>
                {% if class.students.all %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {% for student in class.students.all %}
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <div class="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10 mr-3 flex items-center justify-center">
                                    <i class="fas fa-user-graduate text-gray-400"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium">
                                        <a href="{% url 'center:student_detail' student.pk %}" class="text-primary hover:text-secondary">
                                            {{ student.full_name }}
                                        </a>
                                    </h4>
                                    <p class="text-sm text-gray-500">{{ student.email }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                    {{ student.get_level_display }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-8">No students enrolled yet</p>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Stats</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500">Enrollment</span>
                        <span class="font-bold text-blue-600">{{ class.current_enrollment }}/{{ class.capacity }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Capacity</span>
                        <span class="font-bold">
                            {% widthratio class.current_enrollment class.capacity 100 %}%
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Available</span>
                        <span class="font-bold text-green-600">{{ class.available_spots }}</span>
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-500 mb-1">
                        <span>Enrollment Progress</span>
                        <span>{% widthratio class.current_enrollment class.capacity 100 %}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: {% widthratio class.current_enrollment class.capacity 100 %}%"></div>
                    </div>
                </div>
            </div>

            <!-- Teacher Info -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Teacher</h3>
                <div class="flex items-center">
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-12 h-12 mr-4 flex items-center justify-center">
                        <i class="fas fa-chalkboard-teacher text-gray-400"></i>
                    </div>
                    <div>
                        <h4 class="font-medium">
                            <a href="{% url 'center:teacher_detail' class.teacher.pk %}" class="text-primary hover:text-secondary">
                                {{ class.teacher.name }}
                            </a>
                        </h4>
                        <p class="text-sm text-gray-500">{{ class.teacher.subject_specialization }}</p>
                        <p class="text-sm text-gray-500">{{ class.teacher.email }}</p>
                    </div>
                </div>
            </div>

            <!-- Schedule Info -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Schedule</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <i class="fas fa-calendar text-gray-400 mr-3"></i>
                        <span>{{ class.get_day_of_week_display }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-clock text-gray-400 mr-3"></i>
                        <span>{{ class.start_time|time:"H:i" }} - {{ class.end_time|time:"H:i" }}</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-hourglass-half text-gray-400 mr-3"></i>
                        <span>1.5 hours</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Actions</h3>
                <div class="space-y-2">
                    <button class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg text-center hover:bg-blue-200 transition duration-300">
                        <i class="fas fa-user-plus mr-2"></i> Add Student
                    </button>
                    <a href="{% url 'center:class_update' class.pk %}" class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-center hover:bg-gray-200 transition duration-300">
                        <i class="fas fa-edit mr-2"></i> Edit Class
                    </a>
                    <button class="block w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg text-center hover:bg-green-200 transition duration-300">
                        <i class="fas fa-file-export mr-2"></i> Export List
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
