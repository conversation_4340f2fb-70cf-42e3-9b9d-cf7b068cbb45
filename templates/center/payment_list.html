{% extends 'center/base.html' %}

{% block title %}Payments - English Language Center{% endblock %}
{% block page_title %}Payments Management{% endblock %}

{% block content %}
<div class="bg-white rounded-xl shadow-md p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div class="flex space-x-2 mb-4 md:mb-0">
            <a href="{% url 'center:payment_create' %}" class="px-4 py-2 bg-primary text-white rounded-lg flex items-center hover:bg-secondary transition duration-300">
                <i class="fas fa-plus mr-2"></i> Add Payment
            </a>
        </div>
    </div>
</div>

<div class="bg-white rounded-xl shadow-md overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Next Due</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for payment in payments %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ payment.student.full_name }}</div>
                        <div class="text-sm text-gray-500">{{ payment.student.email }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${{ payment.amount }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ payment.payment_date|date:"M d, Y" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ payment.get_payment_method_display }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if payment.is_confirmed %}
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Confirmed</span>
                        {% else %}
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Pending</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ payment.next_due_date|date:"M d, Y" }}
                        {% if payment.is_overdue %}
                            <span class="text-red-500 text-xs">(Overdue)</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <a href="{% url 'center:payment_update' payment.pk %}" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</a>
                        <a href="{% url 'center:payment_detail' payment.pk %}" class="text-gray-600 hover:text-gray-900">View</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <i class="fas fa-receipt text-6xl text-gray-300 mb-4"></i>
                        <h3 class="text-xl font-medium text-gray-500 mb-2">No payments found</h3>
                        <a href="{% url 'center:payment_create' %}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                            <i class="fas fa-plus mr-2"></i> Add Payment
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
