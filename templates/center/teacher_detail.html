{% extends 'center/base.html' %}

{% block title %}{{ teacher.name }} - Teacher Details{% endblock %}
{% block page_title %}Teacher Profile{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Teacher Header -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
        <div class="flex justify-between items-start">
            <div class="flex items-center">
                <div class="bg-gray-200 border-2 border-dashed rounded-xl w-20 h-20 mr-6 flex items-center justify-center">
                    <i class="fas fa-chalkboard-teacher text-gray-400 text-2xl"></i>
                </div>
                <div>
                    <h1 class="text-2xl font-bold">{{ teacher.name }}</h1>
                    <p class="text-gray-500">{{ teacher.email }}</p>
                    <p class="text-gray-600 mt-1">{{ teacher.subject_specialization }}</p>
                    <div class="flex items-center mt-2">
                        {% if teacher.is_active %}
                            <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Active</span>
                        {% else %}
                            <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">Inactive</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'center:teacher_update' teacher.pk %}" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    <i class="fas fa-edit mr-2"></i> Edit
                </a>
                <a href="{% url 'center:teacher_list' %}" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Contact Information -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4">Contact Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-gray-500 text-sm">Full Name</label>
                        <p class="font-medium">{{ teacher.name }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Email</label>
                        <p class="font-medium">{{ teacher.email }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Phone Number</label>
                        <p class="font-medium">{{ teacher.phone_number }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Specialization</label>
                        <p class="font-medium">{{ teacher.subject_specialization }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Date Joined</label>
                        <p class="font-medium">{{ teacher.date_joined|date:"F d, Y" }}</p>
                    </div>
                    <div>
                        <label class="block text-gray-500 text-sm">Status</label>
                        <p class="font-medium">
                            {% if teacher.is_active %}
                                <span class="text-green-600">Active</span>
                            {% else %}
                                <span class="text-red-600">Inactive</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Teaching Classes -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Teaching Classes</h3>
                {% if teacher.classes.all %}
                    <div class="space-y-3">
                        {% for class in teacher.classes.all %}
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div>
                                <h4 class="font-medium">{{ class.class_name }}</h4>
                                <p class="text-sm text-gray-500">{{ class.get_level_display }}</p>
                                <p class="text-sm text-gray-500">{{ class.schedule_display }}</p>
                            </div>
                            <div class="text-right">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                    {{ class.current_enrollment }}/{{ class.capacity }} students
                                </span>
                                <p class="text-xs text-gray-500 mt-1">
                                    {% if class.is_full %}
                                        <span class="text-red-500">Full</span>
                                    {% else %}
                                        {{ class.available_spots }} spots available
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-8">No classes assigned</p>
                {% endif %}
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Stats -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Stats</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500">Active Classes</span>
                        <span class="font-bold text-blue-600">{{ teacher.active_classes_count }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Total Students</span>
                        <span class="font-bold">
                            {% with total_students=teacher.classes.all|length %}
                                {{ total_students }}
                            {% endwith %}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-500">Experience</span>
                        <span class="font-bold">
                            {% now "Y" as current_year %}
                            {% with years=teacher.date_joined.year %}
                                {{ current_year|add:"-"|add:years }} years
                            {% endwith %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Schedule Overview -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Weekly Schedule</h3>
                {% if teacher.classes.all %}
                    <div class="space-y-2">
                        {% for class in teacher.classes.all %}
                        <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                            <div>
                                <p class="font-medium text-sm">{{ class.get_day_of_week_display }}</p>
                                <p class="text-xs text-gray-500">{{ class.class_name }}</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm">{{ class.start_time|time:"H:i" }} - {{ class.end_time|time:"H:i" }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-4">No scheduled classes</p>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-md p-6">
                <h3 class="text-lg font-bold mb-4">Quick Actions</h3>
                <div class="space-y-2">
                    <a href="{% url 'center:class_create' %}?teacher={{ teacher.pk }}" class="block w-full px-4 py-2 bg-blue-100 text-blue-700 rounded-lg text-center hover:bg-blue-200 transition duration-300">
                        <i class="fas fa-plus mr-2"></i> Assign Class
                    </a>
                    <a href="mailto:{{ teacher.email }}" class="block w-full px-4 py-2 bg-green-100 text-green-700 rounded-lg text-center hover:bg-green-200 transition duration-300">
                        <i class="fas fa-envelope mr-2"></i> Send Email
                    </a>
                    <a href="{% url 'center:teacher_update' teacher.pk %}" class="block w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-center hover:bg-gray-200 transition duration-300">
                        <i class="fas fa-edit mr-2"></i> Edit Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
