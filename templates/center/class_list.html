{% extends 'center/base.html' %}

{% block title %}Classes - English Language Center{% endblock %}
{% block page_title %}Classes Management{% endblock %}

{% block content %}
<div class="bg-white rounded-xl shadow-md p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div class="flex space-x-2 mb-4 md:mb-0">
            <a href="{% url 'center:class_create' %}" class="px-4 py-2 bg-primary text-white rounded-lg flex items-center hover:bg-secondary transition duration-300">
                <i class="fas fa-plus mr-2"></i> Add Class
            </a>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
    {% for class in classes %}
    <div class="class-card bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h3 class="font-bold text-lg">{{ class.class_name }}</h3>
                <p class="text-gray-500">{{ class.get_level_display }}</p>
            </div>
            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                {{ class.current_enrollment }}/{{ class.capacity }}
            </span>
        </div>
        
        <div class="space-y-3 mb-4">
            <div class="flex items-center">
                <i class="fas fa-chalkboard-teacher text-gray-400 mr-2"></i>
                <span>{{ class.teacher.name }}</span>
            </div>
            <div class="flex items-center">
                <i class="fas fa-calendar text-gray-400 mr-2"></i>
                <span>{{ class.schedule_display }}</span>
            </div>
            <div class="flex items-center">
                <i class="fas fa-users text-gray-400 mr-2"></i>
                <span>{{ class.current_enrollment }} students enrolled</span>
            </div>
        </div>
        
        <div class="flex justify-between items-center">
            <a href="{% url 'center:class_detail' class.pk %}" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-sm hover:bg-gray-200 transition duration-200">
                View Details
            </a>
            <div class="flex space-x-2">
                <a href="{% url 'center:class_update' class.pk %}" class="px-3 py-1 bg-primary text-white rounded-lg text-sm hover:bg-secondary transition duration-200">
                    Edit
                </a>
                <a href="{% url 'center:class_delete' class.pk %}" class="px-3 py-1 bg-red-600 text-white rounded-lg text-sm hover:bg-red-700 transition duration-200" onclick="return confirm('Are you sure you want to delete this class?')">
                    <i class="fas fa-trash text-xs"></i>
                </a>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-span-full text-center py-12">
        <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-medium text-gray-500 mb-2">No classes found</h3>
        <a href="{% url 'center:class_create' %}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
            <i class="fas fa-plus mr-2"></i> Add Class
        </a>
    </div>
    {% endfor %}
</div>
{% endblock %}
