{% extends 'center/base.html' %}

{% block title %}{% if object %}Edit Class{% else %}Add Class{% endif %} - English Language Center{% endblock %}
{% block page_title %}{% if object %}Edit Class{% else %}Add New Class{% endif %}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold">{% if object %}Edit Class: {{ object.class_name }}{% else %}Add New Class{% endif %}</h3>
            <a href="{% url 'center:class_list' %}" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </a>
        </div>
        
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.class_name.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Class Name <span class="text-red-500">*</span>
                    </label>
                    {{ form.class_name }}
                    {% if form.class_name.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.class_name.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.level.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Level <span class="text-red-500">*</span>
                    </label>
                    {{ form.level }}
                    {% if form.level.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.level.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.teacher.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Teacher <span class="text-red-500">*</span>
                    </label>
                    {{ form.teacher }}
                    {% if form.teacher.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.teacher.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div class="md:col-span-2">
                    <label class="block text-gray-700 font-medium mb-2">
                        Days of Week <span class="text-red-500">*</span>
                    </label>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {% for choice in form.days_of_week %}
                            <div class="flex items-center">
                                {{ choice.tag }}
                                <label for="{{ choice.id_for_label }}" class="ml-2 text-sm text-gray-700 cursor-pointer">
                                    {{ choice.choice_label }}
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                    {% if form.days_of_week.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.days_of_week.errors.0 }}</p>
                    {% endif %}
                    <p class="text-gray-500 text-xs mt-1">{{ form.days_of_week.help_text }}</p>
                </div>
                
                <div>
                    <label for="{{ form.start_time.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Start Time <span class="text-red-500">*</span>
                    </label>
                    {{ form.start_time }}
                    {% if form.start_time.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.start_time.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.end_time.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        End Time <span class="text-red-500">*</span>
                    </label>
                    {{ form.end_time }}
                    {% if form.end_time.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.end_time.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.capacity.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Capacity <span class="text-red-500">*</span>
                    </label>
                    {{ form.capacity }}
                    {% if form.capacity.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.capacity.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div class="flex items-center">
                    {{ form.is_active }}
                    <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-gray-700 font-medium">
                        Active Class
                    </label>
                </div>
            </div>
            
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'center:class_list' %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    {% if object %}Update Class{% else %}Save Class{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
