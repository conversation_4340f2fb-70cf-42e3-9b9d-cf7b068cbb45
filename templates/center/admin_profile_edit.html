{% extends 'center/base.html' %}

{% block title %}Edit Admin Profile{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Edit Profile</h1>
                    <p class="mt-2 text-gray-600">Update your account information</p>
                </div>
                <a href="{% url 'center:admin_profile' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Profile
                </a>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-purple-600">
                <h2 class="text-xl font-semibold text-white">
                    <i class="fas fa-user-edit mr-2"></i>Profile Information
                </h2>
            </div>
            
            <form method="post" class="p-6">
                {% csrf_token %}
                
                {% if messages %}
                    {% for message in messages %}
                        <div class="mb-4 p-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            <i class="fas fa-info-circle mr-2"></i>{{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <div class="space-y-6">
                    <!-- Username (Read-only) -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2"></i>Username
                        </label>
                        <input type="text" value="{{ user.username }}" readonly 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-600 cursor-not-allowed">
                        <p class="mt-1 text-sm text-gray-500">Username cannot be changed</p>
                    </div>

                    <!-- First Name -->
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2"></i>First Name
                        </label>
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.first_name.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Last Name -->
                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2"></i>Last Name
                        </label>
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.last_name.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-envelope mr-2"></i>Email Address
                        </label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.email.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Account Info (Read-only) -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-sm font-medium text-gray-700 mb-3">Account Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">Date Joined:</span>
                                <span class="text-gray-900 ml-2">{{ user.date_joined|date:"F d, Y" }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">Last Login:</span>
                                <span class="text-gray-900 ml-2">{{ user.last_login|date:"F d, Y g:i A"|default:"Never" }}</span>
                            </div>
                            <div>
                                <span class="text-gray-500">User Type:</span>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 ml-2">
                                    Administrator
                                </span>
                            </div>
                            <div>
                                <span class="text-gray-500">Status:</span>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 ml-2">
                                    Active
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="mt-8 flex space-x-4">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition duration-200">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                    <a href="{% url 'center:admin_profile' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition duration-200">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>

        <!-- Security Notice -->
        <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Security Notice</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>To change your password or other security settings, please contact the system administrator or use the Django admin interface.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Form styling */
input[type="text"], input[type="email"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

input[type="text"]:focus, input[type="email"]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
{% endblock %}
