{% extends 'center/base.html' %}

{% block title %}Students - English Language Center{% endblock %}
{% block page_title %}Students Management{% endblock %}

{% block header_actions %}
<div class="relative">
    <input type="text" name="search" placeholder="Search students..." 
           value="{{ request.GET.search }}" 
           class="bg-gray-100 rounded-full py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-primary"
           onchange="this.form.submit()">
    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
</div>
{% endblock %}

{% block content %}
<!-- Student Filters and Actions -->
<div class="bg-white rounded-xl shadow-md p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div class="flex space-x-2 mb-4 md:mb-0">
            <a href="{% url 'center:student_create' %}" class="px-4 py-2 bg-primary text-white rounded-lg flex items-center hover:bg-secondary transition duration-300">
                <i class="fas fa-plus mr-2"></i> Add Student
            </a>
            <button class="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50 transition duration-300">
                <i class="fas fa-filter mr-2"></i> Filter
            </button>
            <button class="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50 transition duration-300">
                <i class="fas fa-download mr-2"></i> Export
            </button>
        </div>
        
        <form method="get" class="flex items-center space-x-4">
            <div class="relative">
                <select name="level" class="bg-gray-100 border-0 rounded-lg py-2 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary" onchange="this.form.submit()">
                    <option value="">All Levels</option>
                    {% for value, label in search_form.level.field.choices %}
                        {% if value %}
                            <option value="{{ value }}" {% if request.GET.level == value %}selected{% endif %}>{{ label }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
            </div>
            
            <div class="relative">
                <select name="is_active" class="bg-gray-100 border-0 rounded-lg py-2 pl-3 pr-8 focus:outline-none focus:ring-2 focus:ring-primary" onchange="this.form.submit()">
                    <option value="">All Status</option>
                    <option value="true" {% if request.GET.is_active == 'true' %}selected{% endif %}>Active</option>
                    <option value="false" {% if request.GET.is_active == 'false' %}selected{% endif %}>Inactive</option>
                </select>
            </div>
            
            <input type="hidden" name="search" value="{{ request.GET.search }}">
        </form>
    </div>
</div>

<!-- Student Tabs -->
<div class="bg-white rounded-xl shadow-md p-6 mb-6">
    <div class="border-b border-gray-200">
        <nav class="flex space-x-8">
            <button class="py-4 px-1 border-b-2 font-medium text-sm active-tab">
                All Students ({{ total_students }})
            </button>
            <button class="py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                Active ({{ students|length }})
            </button>
        </nav>
    </div>
</div>

<!-- Student Cards Grid -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-6">
    {% for student in students %}
    <div class="student-card bg-white rounded-xl shadow-md overflow-hidden transition duration-300">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-12 h-12 mr-3 flex items-center justify-center">
                        <i class="fas fa-user text-gray-400"></i>
                    </div>
                    <div>
                        <h3 class="font-bold">{{ student.full_name }}</h3>
                        <p class="text-sm text-gray-500">{{ student.email }}</p>
                    </div>
                </div>
                <div class="dropdown relative">
                    <button class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="toggleDropdown(this)">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                        <div class="py-1">
                            <a href="{% url 'center:student_update' student.pk %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                            <a href="{% url 'center:student_detail' student.pk %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Payment History</a>
                            <a href="{% url 'center:student_delete' student.pk %}" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-500">Level:</span>
                    <span class="font-medium">
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">{{ student.get_level_display }}</span>
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500">Phone:</span>
                    <span class="font-medium">{{ student.phone_number }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500">Status:</span>
                    <span class="font-medium">
                        {% if student.is_active %}
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>
                        {% else %}
                            <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Inactive</span>
                        {% endif %}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500">Joined:</span>
                    <span class="font-medium">{{ student.registration_date|date:"d M Y" }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500">Age:</span>
                    <span class="font-medium">{{ student.age }} years</span>
                </div>
            </div>
            
            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                <a href="mailto:{{ student.email }}" class="px-3 py-1 bg-blue-50 text-blue-600 rounded-lg text-sm flex items-center hover:bg-blue-100 transition duration-300">
                    <i class="fas fa-envelope mr-1"></i> Message
                </a>
                <a href="{% url 'center:student_detail' student.pk %}" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-sm flex items-center hover:bg-gray-200 transition duration-300">
                    <i class="fas fa-eye mr-1"></i> View
                </a>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-span-full text-center py-12">
        <i class="fas fa-user-graduate text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-medium text-gray-500 mb-2">No students found</h3>
        <p class="text-gray-400 mb-4">Get started by adding your first student</p>
        <a href="{% url 'center:student_create' %}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
            <i class="fas fa-plus mr-2"></i> Add Student
        </a>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="bg-white rounded-xl shadow-md p-6 flex justify-between items-center">
    <p class="text-gray-500">
        Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} students
    </p>
    <div class="flex space-x-2">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}" 
               class="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50 transition duration-300">
                <i class="fas fa-chevron-left mr-2"></i> Previous
            </a>
        {% endif %}
        
        {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
                <span class="px-4 py-2 bg-primary text-white rounded-lg">{{ num }}</span>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}" 
                   class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition duration-300">{{ num }}</a>
            {% endif %}
        {% endfor %}
        
        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}" 
               class="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50 transition duration-300">
                Next <i class="fas fa-chevron-right ml-2"></i>
            </a>
        {% endif %}
    </div>
</div>
{% endif %}
{% endblock %}
