{% extends 'center/base.html' %}

{% block title %}Delete Class - English Language Center{% endblock %}
{% block page_title %}Delete Class{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="text-center mb-6">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-900">Delete Class</h3>
            <p class="text-gray-500 mt-2">Are you sure you want to delete this class? This action cannot be undone.</p>
        </div>

        <!-- Class Info -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div>
                <h4 class="font-bold">{{ class.class_name }}</h4>
                <p class="text-sm text-gray-500">{{ class.get_level_display }} • {{ class.teacher.name }}</p>
                <p class="text-sm text-gray-500">{{ class.schedule_display }}</p>
                <p class="text-sm text-gray-500">{{ class.current_enrollment }} students enrolled</p>
            </div>
        </div>

        <!-- Dependencies Check -->
        {% if has_dependencies %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Cannot Delete Class</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>This class cannot be deleted because:</p>
                        <ul class="list-disc list-inside mt-1">
                            <li>{{ student_count }} active student(s) are enrolled in this class</li>
                        </ul>
                        <p class="mt-2">
                            <strong>Options:</strong>
                        </p>
                        <ul class="list-disc list-inside mt-1">
                            <li>Remove all students from this class first, then delete</li>
                            <li>Or deactivate the class instead of deleting it</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <!-- Warning -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>Deleting this class will:</p>
                        <ul class="list-disc list-inside mt-1">
                            <li>Permanently remove this class from the system</li>
                            <li>Remove class from teacher's schedule</li>
                            <li>Cancel all future sessions</li>
                        </ul>
                        <p class="mt-2 font-medium">This action cannot be undone!</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Confirmation Form -->
        <form method="post">
            {% csrf_token %}
            <div class="flex justify-end space-x-4">
                <a href="{% url 'center:class_detail' class.pk %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    Cancel
                </a>
                {% if not has_dependencies %}
                <button type="submit" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300">
                    <i class="fas fa-trash mr-2"></i> Delete Class
                </button>
                {% else %}
                <button type="button" disabled class="px-6 py-2 bg-gray-400 text-white rounded-lg cursor-not-allowed">
                    <i class="fas fa-ban mr-2"></i> Cannot Delete
                </button>
                <a href="{% url 'center:class_update' class.pk %}" class="px-6 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition duration-300">
                    <i class="fas fa-edit mr-2"></i> Deactivate Instead
                </a>
                {% endif %}
            </div>
        </form>
    </div>
</div>
{% endblock %}
