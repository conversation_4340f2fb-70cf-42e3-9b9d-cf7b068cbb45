{% extends 'center/base.html' %}

{% block title %}Edit Settings{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Edit Settings</h1>
                    <p class="mt-2 text-gray-600">Configure your language center settings</p>
                </div>
                <a href="{% url 'center:settings' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Settings
                </a>
            </div>
        </div>

        <form method="post" class="space-y-8">
            {% csrf_token %}

            {% if messages %}
                {% for message in messages %}
                    <div class="p-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                        <i class="fas fa-info-circle mr-2"></i>{{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            {% if form.non_field_errors %}
                <div class="p-4 rounded-lg bg-red-100 text-red-700">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    {% for error in form.non_field_errors %}
                        {{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Email & SMTP Settings -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-purple-600">
                    <h2 class="text-xl font-semibold text-white">
                        <i class="fas fa-envelope mr-2"></i>Email Configuration
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-toggle-on mr-2"></i>Enable Email Notifications
                            </label>
                            {{ form.email_enabled }}
                            {% if form.email_enabled.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.email_enabled.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-envelope mr-2"></i>From Email Address
                            </label>
                            {{ form.from_email }}
                            {% if form.from_email.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.from_email.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-reply mr-2"></i>Reply-To Email Address
                            </label>
                            {{ form.reply_to_email }}
                            {% if form.reply_to_email.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.reply_to_email.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- SMTP Settings -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-500 to-blue-500">
                    <h2 class="text-xl font-semibold text-white">
                        <i class="fas fa-server mr-2"></i>SMTP Configuration
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-server mr-2"></i>SMTP Host
                            </label>
                            {{ form.smtp_host }}
                            {% if form.smtp_host.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.smtp_host.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-plug mr-2"></i>SMTP Port
                            </label>
                            {{ form.smtp_port }}
                            {% if form.smtp_port.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.smtp_port.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-user mr-2"></i>SMTP Username
                            </label>
                            {{ form.smtp_username }}
                            {% if form.smtp_username.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.smtp_username.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-key mr-2"></i>SMTP Password
                            </label>
                            {{ form.smtp_password }}
                            {% if form.smtp_password.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.smtp_password.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-shield-alt mr-2"></i>Use TLS
                            </label>
                            {{ form.smtp_use_tls }}
                            {% if form.smtp_use_tls.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.smtp_use_tls.errors.0 }}</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-lock mr-2"></i>Use SSL
                            </label>
                            {{ form.smtp_use_ssl }}
                            {% if form.smtp_use_ssl.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.smtp_use_ssl.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Settings -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-yellow-500 to-orange-500">
                    <h2 class="text-xl font-semibold text-white">
                        <i class="fas fa-credit-card mr-2"></i>Payment Settings
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-dollar-sign mr-2"></i>Default Currency
                            </label>
                            <select name="default_currency" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="USD" selected>USD ($)</option>
                                <option value="EUR">EUR (€)</option>
                                <option value="GBP">GBP (£)</option>
                                <option value="CAD">CAD (C$)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-alt mr-2"></i>Payment Due Days
                            </label>
                            <input type="number" name="payment_due_days" value="30" min="1" max="365" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Late Payment Fee
                            </label>
                            <input type="number" name="late_payment_fee" value="25" min="0" step="0.01" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-percentage mr-2"></i>Early Payment Discount (%)
                            </label>
                            <input type="number" name="early_payment_discount" value="5" min="0" max="50" step="0.1" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Reminder Settings -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-500 to-pink-500">
                    <h2 class="text-xl font-semibold text-white">
                        <i class="fas fa-bell mr-2"></i>Payment Reminder Settings
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-alt mr-2"></i>Reminder Days Before Due
                            </label>
                            {{ form.reminder_days_before }}
                            {% if form.reminder_days_before.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.reminder_days_before.errors.0 }}</p>
                            {% endif %}
                            <p class="text-gray-500 text-xs mt-1">Comma-separated days (e.g., 7,3,1)</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Overdue Reminder Days
                            </label>
                            {{ form.overdue_reminder_days }}
                            {% if form.overdue_reminder_days.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.overdue_reminder_days.errors.0 }}</p>
                            {% endif %}
                            <p class="text-gray-500 text-xs mt-1">Comma-separated days after due date (e.g., 1,7,14)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex space-x-4">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>Save Settings
                </button>
                <a href="{% url 'center:settings' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition duration-200">
                    <i class="fas fa-times mr-2"></i>Cancel
                </a>
                <button type="button" onclick="resetToDefaults()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg transition duration-200">
                    <i class="fas fa-undo mr-2"></i>Reset to Defaults
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to their default values?')) {
        // Reset SMTP settings to defaults
        document.querySelector('input[name="smtp_host"]').value = 'smtp.gmail.com';
        document.querySelector('input[name="smtp_port"]').value = '587';
        document.querySelector('input[name="smtp_username"]').value = '';
        document.querySelector('input[name="smtp_password"]').value = '';
        document.querySelector('input[name="smtp_use_tls"]').checked = true;
        document.querySelector('input[name="smtp_use_ssl"]').checked = false;

        // Reset email settings
        document.querySelector('input[name="from_email"]').value = '<EMAIL>';
        document.querySelector('input[name="reply_to_email"]').value = '';
        document.querySelector('input[name="email_enabled"]').checked = true;

        // Reset reminder settings
        document.querySelector('input[name="reminder_days_before"]').value = '7,3,1';
        document.querySelector('input[name="overdue_reminder_days"]').value = '1,7,14';
    }
}
</script>
{% endblock %}
