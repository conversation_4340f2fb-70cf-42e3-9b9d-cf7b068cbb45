{% extends 'center/base.html' %}

{% block title %}Edit Settings{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Edit Settings</h1>
                    <p class="mt-2 text-gray-600">Configure your language center settings</p>
                </div>
                <a href="{% url 'center:settings' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Settings
                </a>
            </div>
        </div>

        <form method="post" class="space-y-8">
            {% csrf_token %}
            
            {% if messages %}
                {% for message in messages %}
                    <div class="p-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                        <i class="fas fa-info-circle mr-2"></i>{{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <!-- General Settings -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-purple-600">
                    <h2 class="text-xl font-semibold text-white">
                        <i class="fas fa-cog mr-2"></i>General Settings
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-school mr-2"></i>Center Name
                            </label>
                            <input type="text" name="center_name" value="English Language Center" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-phone mr-2"></i>Contact Phone
                            </label>
                            <input type="text" name="contact_phone" value="+****************" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-envelope mr-2"></i>Contact Email
                            </label>
                            <input type="email" name="contact_email" value="<EMAIL>" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-map-marker-alt mr-2"></i>Address
                            </label>
                            <input type="text" name="address" value="123 Education Street, Learning City" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Academic Settings -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-500 to-blue-500">
                    <h2 class="text-xl font-semibold text-white">
                        <i class="fas fa-graduation-cap mr-2"></i>Academic Settings
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar mr-2"></i>Academic Year Start
                            </label>
                            <input type="date" name="academic_year_start" value="2025-09-01" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-check mr-2"></i>Academic Year End
                            </label>
                            <input type="date" name="academic_year_end" value="2026-06-30" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-clock mr-2"></i>Default Class Duration (minutes)
                            </label>
                            <input type="number" name="default_class_duration" value="90" min="30" max="180" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-users mr-2"></i>Max Students per Class
                            </label>
                            <input type="number" name="max_students_per_class" value="20" min="1" max="50" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Settings -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-yellow-500 to-orange-500">
                    <h2 class="text-xl font-semibold text-white">
                        <i class="fas fa-credit-card mr-2"></i>Payment Settings
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-dollar-sign mr-2"></i>Default Currency
                            </label>
                            <select name="default_currency" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="USD" selected>USD ($)</option>
                                <option value="EUR">EUR (€)</option>
                                <option value="GBP">GBP (£)</option>
                                <option value="CAD">CAD (C$)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-calendar-alt mr-2"></i>Payment Due Days
                            </label>
                            <input type="number" name="payment_due_days" value="30" min="1" max="365" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Late Payment Fee
                            </label>
                            <input type="number" name="late_payment_fee" value="25" min="0" step="0.01" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-percentage mr-2"></i>Early Payment Discount (%)
                            </label>
                            <input type="number" name="early_payment_discount" value="5" min="0" max="50" step="0.1" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Settings -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-500 to-pink-500">
                    <h2 class="text-xl font-semibold text-white">
                        <i class="fas fa-bell mr-2"></i>Notification Settings
                    </h2>
                </div>
                <div class="p-6 space-y-6">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="email_notifications" id="email_notifications" checked 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="email_notifications" class="ml-2 block text-sm text-gray-900">
                                <i class="fas fa-envelope mr-2"></i>Email Notifications
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" name="payment_reminders" id="payment_reminders" checked 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="payment_reminders" class="ml-2 block text-sm text-gray-900">
                                <i class="fas fa-money-bill mr-2"></i>Payment Reminders
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" name="class_reminders" id="class_reminders" checked 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="class_reminders" class="ml-2 block text-sm text-gray-900">
                                <i class="fas fa-chalkboard mr-2"></i>Class Reminders
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" name="enrollment_notifications" id="enrollment_notifications" checked 
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="enrollment_notifications" class="ml-2 block text-sm text-gray-900">
                                <i class="fas fa-user-plus mr-2"></i>Enrollment Notifications
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex space-x-4">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>Save Settings
                </button>
                <a href="{% url 'center:settings' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition duration-200">
                    <i class="fas fa-times mr-2"></i>Cancel
                </a>
                <button type="button" onclick="resetToDefaults()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg transition duration-200">
                    <i class="fas fa-undo mr-2"></i>Reset to Defaults
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to their default values?')) {
        // Reset form values to defaults
        document.querySelector('input[name="center_name"]').value = 'English Language Center';
        document.querySelector('input[name="contact_phone"]').value = '+****************';
        document.querySelector('input[name="contact_email"]').value = '<EMAIL>';
        document.querySelector('input[name="address"]').value = '123 Education Street, Learning City';
        document.querySelector('input[name="academic_year_start"]').value = '2025-09-01';
        document.querySelector('input[name="academic_year_end"]').value = '2026-06-30';
        document.querySelector('input[name="default_class_duration"]').value = '90';
        document.querySelector('input[name="max_students_per_class"]').value = '20';
        document.querySelector('select[name="default_currency"]').value = 'USD';
        document.querySelector('input[name="payment_due_days"]').value = '30';
        document.querySelector('input[name="late_payment_fee"]').value = '25';
        document.querySelector('input[name="early_payment_discount"]').value = '5';
        
        // Reset checkboxes
        document.querySelector('input[name="email_notifications"]').checked = true;
        document.querySelector('input[name="payment_reminders"]').checked = true;
        document.querySelector('input[name="class_reminders"]').checked = true;
        document.querySelector('input[name="enrollment_notifications"]').checked = true;
    }
}
</script>
{% endblock %}
