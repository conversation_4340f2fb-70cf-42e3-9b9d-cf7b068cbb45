<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}English Language Center{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#1d4ed8',
                        accent: '#10b981',
                        dark: '#1e293b',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar {
            transition: all 0.3s ease;
        }
        .student-card:hover, .teacher-card:hover, .class-card:hover, .payment-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .active-tab {
            border-bottom: 3px solid #3b82f6;
            color: #3b82f6;
        }
        .modal {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        {% block extra_css %}{% endblock %}
    </style>
</head>
<body class="bg-gray-100">
    <!-- Main Container -->
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar bg-dark text-white w-64 space-y-6 py-7 px-2 fixed inset-y-0 left-0 transform -translate-x-full md:translate-x-0 transition duration-200 ease-in-out z-10">
            <div class="flex items-center space-x-2 px-4">
                <i class="fas fa-book-open text-accent text-2xl"></i>
                <h1 class="text-xl font-bold">English Center</h1>
            </div>
            
            <nav>
                <a href="{% url 'center:dashboard' %}" class="flex items-center space-x-2 px-4 py-3 {% if request.resolver_match.url_name == 'dashboard' %}bg-secondary{% else %}hover:bg-gray-700{% endif %} rounded-lg">
                    <i class="fas fa-chart-line"></i>
                    <span>Dashboard</span>
                </a>
                <a href="{% url 'center:student_list' %}" class="flex items-center space-x-2 px-4 py-3 {% if 'student' in request.resolver_match.url_name %}bg-secondary{% else %}hover:bg-gray-700{% endif %} rounded-lg mt-2">
                    <i class="fas fa-user-graduate"></i>
                    <span>Students</span>
                </a>
                <a href="{% url 'center:teacher_list' %}" class="flex items-center space-x-2 px-4 py-3 {% if 'teacher' in request.resolver_match.url_name %}bg-secondary{% else %}hover:bg-gray-700{% endif %} rounded-lg">
                    <i class="fas fa-chalkboard-teacher"></i>
                    <span>Teachers</span>
                </a>
                <a href="{% url 'center:class_list' %}" class="flex items-center space-x-2 px-4 py-3 {% if 'class' in request.resolver_match.url_name %}bg-secondary{% else %}hover:bg-gray-700{% endif %} rounded-lg">
                    <i class="fas fa-users"></i>
                    <span>Classes</span>
                </a>
                <a href="{% url 'center:payment_list' %}" class="flex items-center space-x-2 px-4 py-3 {% if 'payment' in request.resolver_match.url_name %}bg-secondary{% else %}hover:bg-gray-700{% endif %} rounded-lg">
                    <i class="fas fa-receipt"></i>
                    <span>Payments</span>
                </a>
                <a href="#" class="flex items-center space-x-2 px-4 py-3 hover:bg-gray-700 rounded-lg">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </nav>
            
            <div class="absolute bottom-0 w-full left-0 p-4 border-t border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10"></div>
                    <div>
                        <p class="font-medium">{{ user.get_full_name|default:user.username }}</p>
                        <p class="text-sm text-gray-400">{{ user.email }}</p>
                    </div>
                </div>
                <div class="mt-2">
                    <a href="{% url 'logout' %}" class="text-sm text-gray-400 hover:text-white">
                        <i class="fas fa-sign-out-alt mr-1"></i> Logout
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 md:ml-64">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm">
                <div class="flex justify-between items-center p-4">
                    <div class="flex items-center">
                        <button class="md:hidden text-gray-500 focus:outline-none" onclick="toggleSidebar()">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h2 class="text-xl font-semibold ml-2">{% block page_title %}Dashboard{% endblock %}</h2>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        {% block header_actions %}
                        <div class="relative">
                            <input type="text" placeholder="Search..." class="bg-gray-100 rounded-full py-2 px-4 pl-10 focus:outline-none focus:ring-2 focus:ring-primary">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        {% endblock %}
                        <button class="relative text-gray-500">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                        </button>
                        <div class="flex items-center space-x-2">
                            <div class="bg-gray-200 border-2 border-dashed rounded-xl w-8 h-8"></div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Messages -->
            {% if messages %}
                <div class="p-4">
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} bg-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-100 border border-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-400 text-{% if message.tags == 'error' %}red{% elif message.tags == 'success' %}green{% elif message.tags == 'warning' %}yellow{% else %}blue{% endif %}-700 px-4 py-3 rounded mb-4">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <!-- Main Content -->
            <main class="p-4 md:p-6">
                {% block content %}
                {% endblock %}
            </main>
            
            <!-- Footer -->
            <footer class="bg-white border-t p-4 mt-8">
                <div class="text-center text-gray-500 text-sm">
                    <p>© 2023 English Language Center Management System. All rights reserved.</p>
                </div>
            </footer>
        </div>
    </div>
    
    <script>
        // Mobile sidebar toggle
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('-translate-x-full');
        }
        
        // Toggle dropdown menu
        function toggleDropdown(button) {
            const dropdown = button.nextElementSibling;
            dropdown.classList.toggle('hidden');
            
            // Close other dropdowns
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                if (menu !== dropdown) {
                    menu.classList.add('hidden');
                }
            });
        }
        
        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.classList.add('hidden');
                });
            }
        });
        
        {% block extra_js %}{% endblock %}
    </script>
</body>
</html>
