{% extends 'center/base.html' %}

{% block title %}{% if object %}Edit Student{% else %}Add Student{% endif %} - English Language Center{% endblock %}
{% block page_title %}{% if object %}Edit Student{% else %}Add New Student{% endif %}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Form Card -->
    <div class="bg-white rounded-xl shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold">{% if object %}Edit Student: {{ object.full_name }}{% else %}Add New Student{% endif %}</h3>
            <a href="{% url 'center:student_list' %}" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </a>
        </div>
        
        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Full Name -->
                <div>
                    <label for="{{ form.full_name.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Full Name <span class="text-red-500">*</span>
                    </label>
                    {{ form.full_name }}
                    {% if form.full_name.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.full_name.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Email -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.email.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Phone Number -->
                <div>
                    <label for="{{ form.phone_number.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Phone Number <span class="text-red-500">*</span>
                    </label>
                    {{ form.phone_number }}
                    {% if form.phone_number.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.phone_number.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Gender -->
                <div>
                    <label for="{{ form.gender.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Gender <span class="text-red-500">*</span>
                    </label>
                    {{ form.gender }}
                    {% if form.gender.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.gender.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Date of Birth -->
                <div>
                    <label for="{{ form.date_of_birth.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        Date of Birth <span class="text-red-500">*</span>
                    </label>
                    {{ form.date_of_birth }}
                    {% if form.date_of_birth.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.date_of_birth.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <!-- Level -->
                <div>
                    <label for="{{ form.level.id_for_label }}" class="block text-gray-700 font-medium mb-2">
                        English Level <span class="text-red-500">*</span>
                    </label>
                    {{ form.level }}
                    {% if form.level.errors %}
                        <p class="text-red-500 text-sm mt-1">{{ form.level.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
            
            <!-- Active Status -->
            <div class="flex items-center">
                <div class="flex items-center">
                    {{ form.is_active }}
                    <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-gray-700 font-medium">
                        Active Student
                    </label>
                </div>
                {% if form.is_active.errors %}
                    <p class="text-red-500 text-sm ml-4">{{ form.is_active.errors.0 }}</p>
                {% endif %}
            </div>
            
            <!-- Form Actions -->
            <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'center:student_list' %}" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                    Cancel
                </a>
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
                    {% if object %}Update Student{% else %}Save Student{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-format phone number
    document.getElementById('{{ form.phone_number.id_for_label }}').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length >= 10) {
            value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
        }
        e.target.value = value;
    });
</script>
{% endblock %}
