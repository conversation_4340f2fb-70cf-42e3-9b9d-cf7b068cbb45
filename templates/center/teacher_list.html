{% extends 'center/base.html' %}

{% block title %}Teachers - English Language Center{% endblock %}
{% block page_title %}Teachers Management{% endblock %}

{% block content %}
<!-- Teacher Actions -->
<div class="bg-white rounded-xl shadow-md p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div class="flex space-x-2 mb-4 md:mb-0">
            <a href="{% url 'center:teacher_create' %}" class="px-4 py-2 bg-primary text-white rounded-lg flex items-center hover:bg-secondary transition duration-300">
                <i class="fas fa-plus mr-2"></i> Add Teacher
            </a>
            <button class="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50 transition duration-300">
                <i class="fas fa-filter mr-2"></i> Filter
            </button>
            <a href="{% url 'center:export_teachers' %}" class="px-4 py-2 border border-gray-300 rounded-lg flex items-center hover:bg-gray-50 transition duration-300">
                <i class="fas fa-download mr-2"></i> Export
            </a>
            </button>
        </div>
    </div>
</div>

<!-- Teachers Grid -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-6">
    {% for teacher in teachers %}
    <div class="teacher-card bg-white rounded-xl shadow-md overflow-hidden transition duration-300">
        <div class="p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-12 h-12 mr-3 flex items-center justify-center">
                        <i class="fas fa-chalkboard-teacher text-gray-400"></i>
                    </div>
                    <div>
                        <h3 class="font-bold">{{ teacher.name }}</h3>
                        <p class="text-sm text-gray-500">{{ teacher.email }}</p>
                    </div>
                </div>
                <div class="dropdown relative">
                    <button class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="toggleDropdown(this)">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <div class="dropdown-menu absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                        <div class="py-1">
                            <a href="{% url 'center:teacher_update' teacher.pk %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit</a>
                            <a href="{% url 'center:teacher_detail' teacher.pk %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Classes</a>
                            <a href="{% url 'center:teacher_delete' teacher.pk %}" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-500">Specialization:</span>
                    <span class="font-medium text-sm">{{ teacher.subject_specialization }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500">Phone:</span>
                    <span class="font-medium">{{ teacher.phone_number }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500">Status:</span>
                    <span class="font-medium">
                        {% if teacher.is_active %}
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Active</span>
                        {% else %}
                            <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Inactive</span>
                        {% endif %}
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500">Classes:</span>
                    <span class="font-medium">{{ teacher.active_classes_count }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-500">Joined:</span>
                    <span class="font-medium">{{ teacher.date_joined|date:"d M Y" }}</span>
                </div>
            </div>
            
            <div class="mt-4 pt-4 border-t border-gray-100 flex justify-between">
                <a href="mailto:{{ teacher.email }}" class="px-3 py-1 bg-blue-50 text-blue-600 rounded-lg text-sm flex items-center hover:bg-blue-100 transition duration-300">
                    <i class="fas fa-envelope mr-1"></i> Message
                </a>
                <a href="{% url 'center:teacher_detail' teacher.pk %}" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-sm flex items-center hover:bg-gray-200 transition duration-300">
                    <i class="fas fa-eye mr-1"></i> View
                </a>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-span-full text-center py-12">
        <i class="fas fa-chalkboard-teacher text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-xl font-medium text-gray-500 mb-2">No teachers found</h3>
        <p class="text-gray-400 mb-4">Get started by adding your first teacher</p>
        <a href="{% url 'center:teacher_create' %}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-secondary transition duration-300">
            <i class="fas fa-plus mr-2"></i> Add Teacher
        </a>
    </div>
    {% endfor %}
</div>
{% endblock %}
