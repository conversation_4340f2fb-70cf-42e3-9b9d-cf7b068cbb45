{% extends 'center/base.html' %}

{% block title %}Payment History - {{ student.first_name }} {{ student.last_name }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Payment History</h1>
                    <p class="mt-2 text-gray-600">{{ student.first_name }} {{ student.last_name }}</p>
                </div>
                <a href="{% url 'center:student_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Students
                </a>
            </div>
        </div>

        <!-- Student Info Card -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <div class="flex items-center">
                <div class="bg-blue-100 rounded-full p-4 mr-4">
                    <i class="fas fa-user-graduate text-blue-600 text-2xl"></i>
                </div>
                <div class="flex-1">
                    <h2 class="text-xl font-bold text-gray-900">{{ student.first_name }} {{ student.last_name }}</h2>
                    <p class="text-gray-600">{{ student.email }}</p>
                    <p class="text-gray-600">{{ student.phone }}</p>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Total Paid</div>
                    <div class="text-2xl font-bold text-green-600">${{ total_paid|floatformat:2 }}</div>
                    {% if pending_payments > 0 %}
                        <div class="text-sm text-orange-600">Pending: ${{ pending_payments|floatformat:2 }}</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Payment History -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Payment History</h3>
            </div>
            
            {% if payments %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for payment in payments %}
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">#{{ payment.id }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-bold text-gray-900">${{ payment.amount|floatformat:2 }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ payment.payment_date|date:"M d, Y" }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">{{ payment.next_due_date|date:"M d, Y" }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        {% if payment.is_confirmed %}
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                <i class="fas fa-check-circle mr-1"></i>Confirmed
                                            </span>
                                        {% elif payment.is_overdue %}
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                                <i class="fas fa-exclamation-triangle mr-1"></i>Overdue
                                            </span>
                                        {% else %}
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                <i class="fas fa-clock mr-1"></i>Pending
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="{% url 'center:payment_detail' payment.pk %}" class="text-blue-600 hover:text-blue-900 mr-3">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="{% url 'center:payment_update' payment.pk %}" class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-receipt text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Payment History</h3>
                    <p class="text-gray-500 mb-4">This student has no payment records yet.</p>
                    <a href="{% url 'center:payment_create' %}?student={{ student.id }}" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>Add Payment
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 flex space-x-4">
            <a href="{% url 'center:payment_create' %}?student={{ student.id }}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>Add New Payment
            </a>
            <a href="{% url 'center:student_detail' student.pk %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition duration-200">
                <i class="fas fa-user mr-2"></i>View Student Profile
            </a>
        </div>
    </div>
</div>
{% endblock %}
