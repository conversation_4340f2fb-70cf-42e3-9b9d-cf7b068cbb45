# Email Notification System Setup Guide

## Overview
The English Language Center Management System now includes an automated email notification system for payment reminders. This system sends professional emails to students when their monthly payments are due or overdue.

## Features
- **Automated Payment Reminders**: Sends emails 7, 3, and 1 days before payment due date
- **Overdue Notifications**: Sends urgent emails 1, 7, and 14 days after payment due date
- **Professional Email Templates**: Beautiful HTML emails with responsive design
- **Duplicate Prevention**: Prevents sending multiple emails for the same payment on the same day
- **Admin Interface**: Full admin control over email notifications and settings
- **Manual Sending**: Ability to manually trigger payment reminders
- **Email Statistics**: Dashboard showing email sending statistics
- **Automatic Confirmation**: Sends confirmation emails when payments are received

## Email Configuration

### 1. Environment Variables
Copy `.env.example` to `.env` and configure your email settings:

```bash
# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=English Language Center <<EMAIL>>
```

### 2. Gmail Setup (Recommended)
1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
   - Use this password in `EMAIL_HOST_PASSWORD`

### 3. Other Email Providers
- **Outlook/Hotmail**: `smtp-mail.outlook.com`, port 587
- **Yahoo**: `smtp.mail.yahoo.com`, port 587
- **Custom SMTP**: Configure according to your provider

## Automated Scheduling

### Option 1: Cron Job (Linux/Mac)
Add to your crontab (`crontab -e`):

```bash
# Send payment reminders daily at 9:00 AM
0 9 * * * cd /path/to/your/project && source venv/bin/activate && python manage.py send_payment_reminders
```

### Option 2: Windows Task Scheduler
1. Open Task Scheduler
2. Create Basic Task
3. Set trigger: Daily at 9:00 AM
4. Action: Start a program
5. Program: `C:\path\to\your\project\venv\Scripts\python.exe`
6. Arguments: `manage.py send_payment_reminders`
7. Start in: `C:\path\to\your\project`

### Option 3: Django-Crontab (Alternative)
Install and configure django-crontab for Django-integrated scheduling.

## Management Commands

### Send Payment Reminders
```bash
# Send all due reminders
python manage.py send_payment_reminders

# Dry run (see what would be sent without sending)
python manage.py send_payment_reminders --dry-run --verbose

# Force send even if notifications are disabled
python manage.py send_payment_reminders --force

# Verbose output with statistics
python manage.py send_payment_reminders --verbose
```

## Admin Interface Features

### Email Notification Management
- View all sent, failed, and pending emails
- Resend failed notifications
- Cancel pending notifications
- Search and filter by student, type, status

### Payment Reminder Settings
- Configure reminder days (default: 7, 3, 1 days before)
- Configure overdue reminder days (default: 1, 7, 14 days after)
- Enable/disable email notifications
- Set from email address
- Send test emails
- Manually trigger payment reminders

### Access Admin Features
1. Go to `/admin/`
2. Navigate to "Email notifications" section
3. Manage settings in "Payment reminder settings"

## Email Templates

### Available Templates
- **Payment Reminder**: Friendly reminder before due date
- **Payment Overdue**: Urgent notification after due date
- **Payment Confirmation**: Automatic confirmation when payment received

### Customization
Templates are located in `templates/center/emails/`:
- `base_email.html` - Base template with styling
- `payment_reminder.html` - Payment reminder template
- `payment_overdue.html` - Overdue notification template
- `payment_confirmation.html` - Payment confirmation template

## Testing

### Test Email Configuration
1. Go to Admin → Payment reminder settings
2. Click "Send test email"
3. Enter your email address
4. Check if email is received

### Test Payment Reminders
```bash
# See what emails would be sent
python manage.py send_payment_reminders --dry-run --verbose

# Send actual emails (if any are due)
python manage.py send_payment_reminders --verbose
```

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check email configuration in `.env`
   - Verify email credentials
   - Check if email notifications are enabled in admin

2. **Gmail authentication errors**
   - Use App Password, not regular password
   - Enable 2-Factor Authentication first
   - Check "Less secure app access" (not recommended)

3. **No emails being sent**
   - Check if there are payments due in the configured reminder periods
   - Verify students have valid email addresses
   - Check if emails were already sent today (duplicate prevention)

4. **HTML not rendering**
   - Ensure email client supports HTML
   - Check email template syntax
   - Test with different email clients

### Debug Mode
For testing, use console backend to see emails in terminal:
```bash
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
```

### File-based Testing
Save emails to files for inspection:
```bash
EMAIL_BACKEND=django.core.mail.backends.filebased.EmailBackend
EMAIL_FILE_PATH=/tmp/app-messages
```

## Security Considerations

1. **Email Credentials**: Store in environment variables, never in code
2. **App Passwords**: Use app-specific passwords for Gmail
3. **HTTPS**: Use HTTPS in production for secure email sending
4. **Rate Limiting**: Consider implementing rate limiting for email sending
5. **Spam Prevention**: Use proper from addresses and SPF/DKIM records

## Monitoring

### Dashboard Statistics
The main dashboard shows:
- Emails sent today
- Failed emails today
- Pending emails
- Weekly email statistics

### Admin Monitoring
- View all email notifications with status
- Monitor failed emails and error messages
- Track email sending patterns
- Generate email reports

## Production Deployment

1. **Configure Real SMTP**: Use production email service
2. **Set up Monitoring**: Monitor email delivery rates
3. **Configure Logging**: Set up proper logging for email events
4. **Backup Settings**: Backup email notification settings
5. **Test Thoroughly**: Test all email scenarios before going live

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Django email documentation
3. Check email provider documentation
4. Test with simple email sending first
5. Review application logs for errors
